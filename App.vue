<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	/* 全局样式 */
	page {
		font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
		background-color: #f8f5f2;
		color: #333;
	}
	
	/* 状态栏样式 */
	.status-bar {
		background-color: white;
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 15px;
		font-size: 14px;
	}
	
	/* 应用头部样式 */
	.app-header {
		display: flex;
		align-items: center;
		padding: 10px 15px;
		background-color: white;
	}
	
	/* 搜索栏样式 */
	.search-bar {
		background-color: #f5f5f5;
		border-radius: 20px;
		padding: 8px 15px;
		display: flex;
		align-items: center;
		margin-left: 10px;
		flex: 1;
	}
	
	/* 导航头部样式 */
	.nav-header {
		display: flex;
		align-items: center;
		padding: 10px 15px;
		background-color: white;
		position: relative;
		justify-content: space-between;
	}
	
	.page-title {
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		font-weight: bold;
		font-size: 18px;
	}
	
	/* Banner样式 */
	.banner {
		height: 180px;
		background-size: cover;
		background-position: center;
		position: relative;
		margin-bottom: 20px;
	}
	
	.banner-text {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: white;
		font-size: 24px;
		font-weight: bold;
		text-align: center;
		text-shadow: 0 2px 4px rgba(0,0,0,0.5);
		width: 100%;
	}
	
	.banner-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
		height: 80px;
		display: flex;
		align-items: flex-end;
		padding: 15px;
		z-index: 10;
	}
	
	.banner-title {
		color: white;
		font-size: 20px;
		font-weight: bold;
	}
	
	/* 服务网格样式 */
	.service-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 15px;
		padding: 0 15px;
	}
	
	.service-item {
		background-color: #f0e6dd;
		border-radius: 10px;
		padding: 15px 10px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
	}
	
	.service-icon {
		width: 40px;
		height: 40px;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 地图Banner样式 */
	.map-banner {
		margin: 20px 15px;
		border-radius: 10px;
		overflow: hidden;
		position: relative;
		height: 120px;
		background-size: cover;
		background-position: center;
	}
	
	.map-text {
		position: absolute;
		top: 50%;
		left: 20px;
		transform: translateY(-50%);
		color: white;
		font-size: 20px;
		font-weight: bold;
		text-shadow: 0 2px 4px rgba(0,0,0,0.5);
	}
	
	/* 紧急按钮样式 */
	.emergency-button {
		width: 60px;
		height: 60px;
		background-color: #c8a287;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4px 10px rgba(0,0,0,0.1);
	}
	
	/* 服务标签样式 */
	.service-tabs {
		display: flex;
		background-color: white;
		padding: 15px;
		margin-bottom: 15px;
		overflow-x: auto;
		white-space: nowrap;
	}
	
	.service-tab {
		padding: 8px 15px;
		margin-right: 10px;
		border-radius: 20px;
		font-size: 14px;
	}
	
	.service-tab.active {
		background-color: #c8a287;
		color: white;
	}
</style>
