<template>
	<view class="container">
		<!-- 应用头部 -->
		<!-- <view class="app-header">
			<view class="flex items-center" @click="goBack">
				<my-icon type="arrow-left" size="24" color="#333"></my-icon>
				<text class="ml-2">返回</text>
			</view>
			<view class="text-center flex-1 font-bold">养老政策</view>
			<view class="w-8"></view> 
		</view> -->
		
		<!-- 搜索栏 -->
		<view class="search-container">
			<view class="search-bar">
				<my-icon type="search" size="18" color="#9ca3af" class="mr-2"></my-icon>
				<input type="text" placeholder="搜索政策关键词" v-model="searchKeyword" class="flex-1" />
				<my-icon v-if="searchKeyword" type="close" size="18" color="#9ca3af" class="ml-auto" @click="clearSearch"></my-icon>
			</view>
		</view>
		
		<!-- 政策分类标签 -->
		<view class="category-tabs">
			<view 
				v-for="(category, index) in categories" 
				:key="index" 
				class="category-tab" 
				:class="{'active': currentCategory === index}"
				@click="selectCategory(index)"
			>
				{{category.name}}
			</view>
		</view>
		
		<!-- 政策列表 -->
		<view class="policy-list">
			<view 
				v-for="(policy, index) in filteredPolicies" 
				:key="index" 
				class="policy-item"
				@click="viewPolicyDetail(policy.id)"
			>
				<view class="policy-title">{{policy.spname}}</view>
				<view class="policy-meta">
					<view class="policy-date">发布时间：{{policy.adddate}}</view>
				</view>
				<view class="policy-summary">{{policy.summary}}</view>
				<view class="policy-footer">
					<view class="read-more">
						<text>查看详情</text>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view v-if="hasMorePolicies" class="load-more" @click="loadMorePolicies">
				加载更多
			</view>
			<view v-else class="no-more">
				没有更多政策了
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			currentCategory: 0,
			categories: [{
				name: '全部', value: 0
			}, {
				name: '上海市政策', value: 1
			},{
				name: '宝山区政策', value: 2
			}, {
				name: '街道政策', value: 3
			}],
			policies: [],
			page: 1,
			pageSize: 5,
			sptype: 1,
			hasMorePolicies: true
		}
	},
	computed: {
		filteredPolicies() {
			let result = this.policies;
			
			// 按分类筛选
			/* if (this.currentCategory !== 0) { // 不是"全部"分类
				const categoryName = this.categories[this.currentCategory];
				result = result.filter(policy => 
					policy.tags.some(tag => tag === categoryName)
				);
			} */
			
			// 按关键词搜索
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.trim().toLowerCase();
				result = result.filter(policy => 
					policy.spname.toLowerCase().includes(keyword));
			}
			
			return result;
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		clearSearch() {
			this.searchKeyword = '';
		},
		selectCategory(index) {
			this.currentCategory = index;
			this.sptype = this.categories[index].value;
			this.page = 1;
			this.policies = [];
			this.hasMorePolicies = true;
			this.loadMorePolicies();
		},
		viewPolicyDetail(id) {
			uni.navigateTo({
				url: `/pages/policy/policy-detail?id=${id}`
			});
		},
		loadMorePolicies() {
			if (this.hasMorePolicies) {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});

				const params = {
					current: this.page,
					size: this.pageSize,
				}
				if(this.sptype !== 0) {
					params.sptype = this.sptype;
				}
				
				// 使用分页参数current和size
				uni.request({
					//url: 'https://bsyltest.shmallshow.com/biz/syspolic/page-out',
					url: this.$backUrl + '/biz/syspolic/page-out',
					data: params,
					header: {},
					success: (res) => {
						if (res.data && res.data.data) {
							console.log(res.data.data);
							if (this.page === 1) {
								// 第一页，直接替换数据
								this.policies = res.data.data.records;
							} else {
								// 不是第一页，追加数据
								this.policies = [...this.policies, ...res.data.data.records];
							}
							
							// 判断是否还有更多数据
							if (res.data.data.records.length < this.pageSize) {
								this.hasMorePolicies = false;
							} else {
								// 页码加1，准备下次加载
								this.page++;
								this.hasMorePolicies = true;
							}
						} else {
							// 没有返回数据，设置没有更多
							this.hasMorePolicies = false;
						}
					},
					fail: (err) => {
						console.error('获取政策列表失败', err);
						uni.showToast({
							title: '获取数据失败',
							icon: 'none'
						});
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			}
		}
	},
	onLoad() {
		this.loadMorePolicies()
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.app-header {
	display: flex;
	align-items: center;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.search-container {
	padding: 10px 15px;
	background-color: white;
	margin-bottom: 10px;
}

.search-bar {
	background-color: #f5f5f5;
	border-radius: 20px;
	padding: 8px 15px;
	display: flex;
	align-items: center;
}

.category-tabs {
	display: flex;
	overflow-x: auto;
	background-color: white;
	padding: 10px 15px;
	margin-bottom: 10px;
}

.category-tab {
	padding: 6px 12px;
	margin-right: 10px;
	border-radius: 16px;
	font-size: 14px;
	white-space: nowrap;
	background-color: #f5f5f5;
	color: #666;
}

.category-tab.active {
	background-color: #C8A287;
	color: white;
}

.policy-list {
	padding: 0 15px;
}

.policy-item {
	background-color: white;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 15px;
}

.policy-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
	line-height: 1.4;
}

.policy-meta {
	display: flex;
	justify-content: space-between;
	font-size: 12px;
	color: #999;
	margin-bottom: 10px;
}

.policy-summary {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
}

.policy-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.policy-tag {
	display: inline-block;
	background-color: #f5f5f5;
	color: #666;
	font-size: 12px;
	padding: 4px 8px;
	border-radius: 4px;
	margin-right: 8px;
}

.read-more {
	display: flex;
	align-items: center;
	font-size: 14px;
	color: #C8A287;
}

.load-more, .no-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #999;
}

.load-more {
	color: #C8A287;
}
</style>
