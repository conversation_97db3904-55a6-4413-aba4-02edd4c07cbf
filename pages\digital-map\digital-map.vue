<template>
	<view class="container">
		<!-- 状态栏 -->
		<!-- <view class="status-bar">
			<view>{{currentTime}}</view>
			<view>
				<text class="iconfont icon-signal"></text>
				<text class="iconfont icon-wifi ml-2"></text>
				<text class="iconfont icon-battery-full ml-2"></text>
			</view>
		</view> -->

		<!-- 应用头部 -->
		<!-- <view class="app-header">
			<view class="w-8 h-8 rounded-full overflow-hidden">
				<image src="/static/images/default-avatar.svg" alt="老爷爷头像" class="w-full h-full"></image>
			</view>
			<view class="search-bar">
				<my-icon type="search" size="18" color="#9ca3af" class="mr-2"></my-icon>
				<text class="text-gray-400">搜索地点、服务或地址</text>
				<text class="iconfont icon-microphone text-gray-400 ml-auto"></text>
			</view>
		</view> -->

		<!-- Banner -->
		<view class="banner">
			<image src="https://kodo.dingsd115.com/bs-uniapp/banner-map.png" alt="老灵光便捷地图" class="w-full h-full"></image> <!-- style="filter: brightness(0.7);" -->
			<!-- <view class="banner-text">老灵光便捷地图</view> -->
		</view>

		<!-- 地图容器 -->
		<view class="map-container">
			<map
				style="width: 100%; height: 100%;"
				:latitude="mapState.latitude"
				:longitude="mapState.longitude"
				:markers="mapState.markers"
				:scale="mapState.scale"
				show-location
				@markertap="onMarkerTap"
			></map>

			<!-- 地图控制按钮 -->
			<view class="map-controls">
				<view class="map-control-button" @click="zoomIn">
					<my-icon type="plus"></my-icon>
				</view>
				<view class="map-control-button" @click="zoomOut">
					<my-icon type="minus"></my-icon>
				</view>
				<view class="map-control-button" @click="locateMe">
					<my-icon type="location-arrow"></my-icon>
				</view>
			</view>
		</view>

		<!-- 服务筛选 -->
		<view class="service-filter">
			<view
				v-for="(filter, index) in filters"
				:key="index"
				:class="['filter-button', filter.active ? 'active' : '']"
				@click="toggleFilter(index)"
			>
				{{filter.name}}
			</view>
		</view>

		<!-- 附近列表 -->
		<view class="nearby-list">
			<view class="nearby-item" v-for="(item, index) in nearbyPlaces" :key="index">
				<view class="nearby-header" @click="viewInstitutionDetail(item.id, item.type)">
					<view class="nearby-title">{{item.name}}</view>
					<!-- <view class="nearby-distance">{{item.distance}}</view> -->
				</view>
				<view class="nearby-address">{{item.address}}</view>
				<view class="nearby-actions">
					<view class="action-button" @click="navigateToLocation(item)">
						<my-icon type="location" size="18" class="mr-1"></my-icon>
						<text>导航</text>
					</view>
					<view class="action-button" @click="callPhone(item.phone)">
						<my-icon type="phone" size="18" class="mr-1"></my-icon>
						<text>电话</text>
					</view>
					<view>
						<button plain="true" type="default" class="action-button"
						style="color: #c8a287;border: none;"
						open-type="share" @click="shareOrganization(item)">
							<my-icon type="share" size="18" class="mr-1"></my-icon>
							<text>分享</text>
						</button>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMorePlaces" class="load-more" @click="loadMorePlaces">
				加载更多
			</view>
			<view v-else-if="nearbyPlaces.length > 0" class="no-more">
				没有更多机构了
			</view>

			<!-- 无数据提示 -->
			<view class="no-data" v-if="nearbyPlaces.length === 0">
				<image src="/static/images/no-data.png" class="no-data-image"></image>
				<text class="no-data-text">暂无机构数据</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentTime: '14:05',
			mapState: {
				latitude: 31.298523,  // 宝山区大致坐标
				longitude: 121.413288,
				scale: 14,
				markers: []
			},
			filters: [
				{ name: '全部', active: true, type: 0 },
				{ name: '食堂', active: false, type: 2 },
				{ name: '医院', active: false, type: 3 },
				{ name: '药店', active: false, type: 4 },
				{ name: '养老院', active: false, type: 10 }
			],
			nearbyPlaces: [],
			page: 1,
			pageSize: 50,
			hasMorePlaces: true,
			currentFilterType: 0, // 当前筛选类型
			shareOrganizationItem: null
		}
	},
	onLoad() {
		//this.updateTime();
		// 设置定时器，每分钟更新一次时间
		//setInterval(this.updateTime, 60000);

		// 加载机构数据
		this.loadMorePlaces();

		// 获取用户位置
		//this.getUserLocation();
	},
	// 分享功能
	onShareAppMessage() {
		return {
			title: this.shareOrganizationItem ? this.shareOrganizationItem.name : '机构详情',
			path: `/pages/institution-detail/institution-detail?id=${this.shareOrganizationItem.id}&type=${this.shareOrganizationItem.type}`
		}
	},
	methods: {
		updateTime() {
			const now = new Date();
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}`;
		},
		// 加载更多机构
		loadMorePlaces() {
			if (this.hasMorePlaces) {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});

				// 构建请求参数
				let params = {
					current: this.page,
					size: this.pageSize
				};

				// 如果选择了特定类别，添加筛选条件
				if (this.currentFilterType !== 0) {
					params.sfdsitetype = this.currentFilterType;
				}

				// 发送请求
				uni.request({
					url: this.$backUrl + '/biz/sysinformationdisplay/page-out',
					data: params,
					header: {},
					success: (res) => {
						if (res.data && res.data.data && res.data.data.records) {
							// 处理返回的机构数据
							const newPlaces = res.data.data.records.map(item => {
								return {
									id: item.id,
									name: item.sfdname || '未命名机构',
									address: item.sfdaddress || '暂无地址信息',
									distance: item.sfddistance || '1公里',
									type: item.sfdsitetype || 0,
									latitude: item.sfdlatitude,
									longitude: item.sfdlongitude,
									phone: item.sfduphone || 'N/A'
								};
							});

							// 创建新的地图标记
							const newMarkers = newPlaces.map((place, index) => {
								// 计算标记ID，避免重复
								const markerId = this.page === 1 ? index : this.mapState.markers.length + index;

								// 根据机构类型选择不同的图标
								let iconPath = '/static/images/icons/location.png';
								switch(place.type) {
									case 1: // 养老院
										iconPath = '/static/images/icons/nursing-home.png';
										break;
									case 2: // 食堂
										iconPath = '/static/images/icons/restaurant.png';
										break;
									case 3: // 医院
										iconPath = '/static/images/icons/hospital.png';
										break;
									case 4: // 活动中心
										iconPath = '/static/images/icons/activity.png';
										break;
									case 5: // 公园
										iconPath = '/static/images/icons/park.png';
										break;
									default:
										iconPath = '/static/images/icons/location.png';
								}

								return {
									id: markerId,
									latitude: place.latitude,
									longitude: place.longitude,
									title: place.name,
									callout: {
										content: place.name,
										display: 'ALWAYS',
										borderWidth: 1,
										borderRadius: 10,
										borderColor: "#333333",
										bgColor: "#ffffff",
										padding: 5
									},
									iconPath: iconPath,
									width: 30,
									height: 30,
									placeId: place.id, // 存储机构ID
									placeType: place.type // 存储机构类型
								};
							});

							if (this.page === 1) {
								// 第一页，直接替换数据
								this.nearbyPlaces = newPlaces;
								this.mapState.markers = newMarkers;
							} else {
								// 不是第一页，追加数据
								this.nearbyPlaces = [...this.nearbyPlaces, ...newPlaces];
								this.mapState.markers = [...this.mapState.markers, ...newMarkers];
							}

							// 判断是否还有更多数据
							if (newPlaces.length < this.pageSize) {
								this.hasMorePlaces = false;
							} else {
								// 页码加1，准备下次加载
								this.page++;
								this.hasMorePlaces = true;
							}
						} else {
							// 没有返回数据，设置没有更多
							this.hasMorePlaces = false;

							// 如果是第一页且没有数据，显示提示信息
							if (this.page === 1) {
								uni.showToast({
									title: '暂无机构数据',
									icon: 'none',
									duration: 2000
								});
							}
						}
					},
					fail: (err) => {
						console.error('获取机构列表失败', err);
						uni.showToast({
							title: '获取机构列表失败',
							icon: 'none',
							duration: 2000
						});

						// 设置没有更多数据
						this.hasMorePlaces = false;
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			}
		},
		getUserLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.mapState.latitude = res.latitude;
					this.mapState.longitude = res.longitude;

					// 添加用户位置标记
					const userMarker = {
						id: 0,
						latitude: res.latitude,
						longitude: res.longitude,
						title: '我的位置',
						iconPath: '/static/images/icons/user-location.png',
						width: 30,
						height: 30
					};

					// 更新标记数组
					this.mapState.markers = [userMarker, ...this.mapState.markers];
				},
				fail: () => {
					uni.showToast({
						title: '获取位置失败',
						icon: 'none'
					});
				}
			});
		},
		zoomIn() {
			if (this.mapState.scale < 18) {
				this.mapState.scale++;
			}
		},
		zoomOut() {
			if (this.mapState.scale > 5) {
				this.mapState.scale--;
			}
		},
		locateMe() {
			this.getUserLocation();
		},
		toggleFilter(index) {
			// 如果点击当前活跃的筛选，不做处理
			if (this.filters[index].active) {
				return;
			}

			// 重置所有筛选
			this.filters.forEach(filter => {
				filter.active = false;
			});

			// 激活选中的筛选
			this.filters[index].active = true;

			// 设置当前筛选类型
			this.currentFilterType = this.filters[index].type;

			// 重置分页并重新加载数据
			this.page = 1;
			this.nearbyPlaces = [];
			this.mapState.markers = [];
			this.hasMorePlaces = true;
			this.loadMorePlaces();
		},

		// 处理地图标记点击事件
		onMarkerTap(e) {
			const markerId = e.detail.markerId;

			// 找到对应的标记
			const marker = this.mapState.markers.find(marker => marker.id === markerId);

			console.log(e)
			if (!marker) return;

			// 如果标记中存储了机构ID和类型，直接使用
			if (marker.placeId) {
				this.viewInstitutionDetail(marker.placeId, marker.placeType);
				return;
			}

			// 如果标记中没有存储机构ID和类型，则根据标记的标题找到对应的机构
			const place = this.nearbyPlaces.find(item => item.name === marker.title);

			if (place) {
				// 跳转到机构详情页面
				this.viewInstitutionDetail(place.id, place.type);
			}
		},

		// 查看机构详情
		viewInstitutionDetail(id, type) {
			if (!id && id !== 0) {
				uni.showToast({
					title: '机构ID不存在',
					icon: 'none'
				});
				return;
			}

			// 跳转到机构详情页面
			uni.navigateTo({
				url: `/pages/institution-detail/institution-detail?id=${id}&type=${type || this.currentFilterType}`
			});
		},
		// 拨打电话
		callPhone(phone) {
			if (phone && phone !== '暂无联系电话') {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			} else {
				uni.showToast({
					title: '暂无联系电话',
					icon: 'none'
				});
			}
		},
		// 导航到机构位置
		navigateToLocation(item) {
			if (item.latitude && item.longitude) {
				uni.openLocation({
					latitude: parseFloat(item.latitude),
					longitude: parseFloat(item.longitude),
					name: item.name,
					address: item.address || '暂无地址信息'
				});
			} else {
				uni.showToast({
					title: '暂无位置信息',
					icon: 'none'
				});
			}
		},
		// 预约服务
		makeAppointment() {
			uni.showToast({
				title: '预约功能开发中',
				icon: 'none'
			});
			// 这里可以跳转到预约页面
			// uni.navigateTo({
			//     url: `/pages/appointment/appointment?id=${this.id}&name=${this.institution.name}`
			// });
		},
		// 分享机构
		shareOrganization(e) {
			this.shareOrganizationItem = e;
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.ml-2 {
	margin-left: 8px;
}

.mr-1 {
	margin-right: 4px;
}

.w-8 {
	width: 32px;
}

.h-8 {
	height: 32px;
}

.rounded-full {
	border-radius: 9999px;
}

.overflow-hidden {
	overflow: hidden;
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

.text-gray-400 {
	color: #9ca3af;
}

.mr-2 {
	margin-right: 8px;
}

.ml-auto {
	margin-left: auto;
}

/* 数字地图页面特定样式 */
.banner {
	height: 80px;
	background-size: cover;
	background-position: center;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20px;
	overflow: hidden;
}

.banner-text {
	color: white;
	font-size: 24px;
	font-weight: bold;
	text-shadow: 0 2px 4px rgba(0,0,0,0.5);
	position: absolute;
	z-index: 10;
}

.map-container {
	margin: 0 15px;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	height: 400px;
	position: relative;
}

.map-controls {
	position: absolute;
	top: 10px;
	right: 10px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	z-index: 10;
}

.map-control-button {
	width: 40px;
	height: 40px;
	background-color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.service-filter {
	margin: 20px 15px;
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.filter-button {
	background-color: white;
	border-radius: 20px;
	padding: 8px 15px;
	font-size: 14px;
	box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.filter-button.active {
	background-color: #c8a287;
	color: white;
}

.nearby-list {
	margin: 20px 15px;
}

.nearby-item {
	background-color: white;
	border-radius: 10px;
	padding: 15px;
	margin-bottom: 15px;
	box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.nearby-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.nearby-title {
	font-weight: bold;
	font-size: 16px;
}

.nearby-distance {
	color: #c8a287;
}

.nearby-address {
	color: #666;
	font-size: 14px;
	margin-bottom: 10px;
}

.nearby-actions {
	display: flex;
	justify-content: space-between;
}

.action-button {
	display: flex;
	align-items: center;
	color: #c8a287;
	font-size: 14px;
}

.mr-2 {
	margin-right: 8px;
}

/* 临时图标样式，实际应使用iconfont */
.iconfont {
	font-family: "fontello";
}

.icon-signal:before {
	content: "\e8d7";
}

.icon-wifi:before {
	content: "\e8d8";
}

.icon-battery-full:before {
	content: "\e8d9";
}

.icon-search:before {
	content: "\E802";
}

.icon-microphone:before {
	content: "\e8db";
}

.icon-plus:before {
	content: "\E819";
}

.icon-minus:before {
	content: "\E81B";
}

.icon-location-arrow:before {
	content: "\E83A";
}

.icon-directions:before {
	content: "\F124";
}

.icon-phone:before {
	content: "\F098";
}

.icon-share:before {
	content: "\E830";
}

/* 加载更多和无数据提示样式 */
.load-more, .no-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #999;
}

.load-more {
	color: #c8a287;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 30px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}
</style>
