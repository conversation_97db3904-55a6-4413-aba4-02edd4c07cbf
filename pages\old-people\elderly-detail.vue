<template>
	<view class="container">
		<!-- 导航栏 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<uni-icons type="arrow-left" size="24" color="#333"></uni-icons>
			</view>
			<view class="page-title">{{ isAddMode ? '添加老人' : '老人详情' }}</view>
			<view class="delete-btn" v-if="!isAddMode" @click="deleteElderly">删除</view>
		</view> -->

		<!-- 老人信息表单 -->
		<view class="elderly-form">
			<!-- 头像 -->
			<!-- <view class="form-item">
				<view class="item-label">头像</view>
				<view class="item-content">
					<image :src="elderlyInfo.avatar || '/static/images/avatar/avatar.svg'" class="avatar-image" @click="editAvatar"></image>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view> -->

			<!-- 姓名 -->
			<view class="form-item" @click="editName">
				<view class="item-label">姓名</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.name || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 年龄 -->
			<view class="form-item" @click="editAge">
				<view class="item-label">年龄</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.age ? elderlyInfo.age + '岁' : '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 性别 -->
			<view class="form-item" @click="editGender">
				<view class="item-label">性别</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.gender || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 关系 -->
			<view class="form-item" @click="editRelation">
				<view class="item-label">关系</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.relation || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 手机号 -->
			<view class="form-item" @click="editPhone">
				<view class="item-label">手机号</view>
				<view class="item-content">
					<text class="item-value">{{ formatPhone(elderlyInfo.phone) }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 身份证号 -->
			<view class="form-item" @click="editIdCard">
				<view class="item-label">身份证号</view>
				<view class="item-content">
					<text class="item-value">{{ formatIdCard(elderlyInfo.idCardNumber) }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 住址 -->
			<view class="form-item" @click="editAddress">
				<view class="item-label">住址</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.homeAddress || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 健康状况 -->
			<view class="form-item" @click="editHealthStatus">
				<view class="item-label">健康状况</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.healthInfo || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 紧急联系人 -->
			<!-- <view class="form-item" @click="editEmergencyContact">
				<view class="item-label">紧急联系人</view>
				<view class="item-content">
					<text class="item-value">{{ elderlyInfo.emergencyContact || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view> -->

			<!-- 紧急联系人电话 -->
			<!-- <view class="form-item" @click="editEmergencyPhone">
				<view class="item-label">紧急联系人电话</view>
				<view class="item-content">
					<text class="item-value">{{ formatPhone(elderlyInfo.emergencyPhone) }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view> -->
		</view>

		<!-- 保存按钮 -->
		<view class="save-button" @click="saveElderlyInfo">
			保存
		</view>

		<!-- 各种编辑弹窗 -->
		<!-- 姓名弹窗 -->
		<uni-popup ref="namePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置姓名</view>
					<view class="popup-close" @click="closePopup('namePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="text"
						v-model="editForm.name"
						placeholder="请输入姓名"
						maxlength="20"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('name')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 年龄弹窗 -->
		<uni-popup ref="agePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置年龄</view>
					<view class="popup-close" @click="closePopup('agePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="number"
						v-model="editForm.age"
						placeholder="请输入年龄"
						maxlength="3"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('age')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 性别弹窗 -->
		<uni-popup ref="genderPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置性别</view>
					<view class="popup-close" @click="closePopup('genderPopup')">取消</view>
				</view>
				<view class="popup-body">
					<radio-group @change="genderChange">
						<label class="radio-item" v-for="(item, index) in genderOptions" :key="index">
							<view class="radio-label">{{ item.label }}</view>
							<radio :value="item.value" :checked="editForm.gender == item.value" color="#c8a287" />
						</label>
					</radio-group>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('gender')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 关系弹窗 -->
		<uni-popup ref="relationPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置关系</view>
					<view class="popup-close" @click="closePopup('relationPopup')">取消</view>
				</view>
				<view class="popup-body">
					<radio-group @change="relationChange">
						<label class="radio-item" v-for="(item, index) in relationOptions" :key="index">
							<view class="radio-label">{{ item.label }}</view>
							<radio :value="item.value" :checked="editForm.relation == item.value" color="#c8a287" />
						</label>
					</radio-group>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('relation')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 手机号弹窗 -->
		<uni-popup ref="phonePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置手机号</view>
					<view class="popup-close" @click="closePopup('phonePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="number"
						v-model="editForm.phone"
						placeholder="请输入手机号"
						maxlength="11"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('phone')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 身份证号弹窗 -->
		<uni-popup ref="idCardPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置身份证号</view>
					<view class="popup-close" @click="closePopup('idCardPopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="idcard"
						v-model="editForm.idCardNumber"
						placeholder="请输入身份证号"
						maxlength="18"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('idCard')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 住址弹窗 -->
		<uni-popup ref="addressPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置住址</view>
					<view class="popup-close" @click="closePopup('addressPopup')">取消</view>
				</view>
				<view class="popup-body">
					<textarea
						class="textarea-field"
						v-model="editForm.homeAddress"
						placeholder="请输入住址"
						maxlength="100"
					/>
					<view class="input-counter">{{ editForm.homeAddress.length }}/100</view>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('address')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 健康状况弹窗 -->
		<uni-popup ref="healthStatusPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置健康状况</view>
					<view class="popup-close" @click="closePopup('healthStatusPopup')">取消</view>
				</view>
				<view class="popup-body">
					<textarea
						class="textarea-field"
						v-model="editForm.healthInfo"
						placeholder="请输入健康状况，如有慢性病请注明"
						maxlength="200"
					/>
					<view class="input-counter">{{ editForm.healthInfo.length }}/200</view>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('healthStatus')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 紧急联系人弹窗 -->
		<uni-popup ref="emergencyContactPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置紧急联系人</view>
					<view class="popup-close" @click="closePopup('emergencyContactPopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="text"
						v-model="editForm.emergencyContact"
						placeholder="请输入紧急联系人姓名"
						maxlength="20"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('emergencyContact')">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 紧急联系人电话弹窗 -->
		<uni-popup ref="emergencyPhonePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置紧急联系人电话</view>
					<view class="popup-close" @click="closePopup('emergencyPhonePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="number"
						v-model="editForm.emergencyPhone"
						placeholder="请输入紧急联系人电话"
						maxlength="11"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmEdit('emergencyPhone')">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {},
			isAddMode: false,
			elderlyId: null,
			elderlyInfo: {
				id: null,
				name: '',
				age: '',
				gender: '',
				relation: '',
				avatar: '',
				phone: '',
				idCardNumber: '',
				homeAddress: '',
				healthInfo: '',
				emergencyContact: '',
				emergencyPhone: ''
			},
			editForm: {
				name: '',
				age: '',
				gender: '',
				relation: '',
				phone: '',
				idCardNumber: '',
				homeAddress: '',
				healthInfo: '',
				emergencyContact: '',
				emergencyPhone: ''
			},
			genderOptions: [
				{ label: '男', value: '男' },
				{ label: '女', value: '女' }
			],
			relationOptions: [
				{ label: '父亲', value: '父亲' },
				{ label: '母亲', value: '母亲' },
				{ label: '岳父', value: '岳父' },
				{ label: '岳母', value: '岳母' },
				{ label: '公公', value: '公公' },
				{ label: '婆婆', value: '婆婆' },
				{ label: '其他', value: '其他' }
			]
		}
	},
	onLoad(options) {
		this.getUserInfo();
		// 判断是添加模式还是编辑模式
		if (options.mode === 'add') {
			this.isAddMode = true;
		} else if (options.id) {
			this.elderlyId = options.id;
			this.getElderlyInfo();
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		getUserInfo() {
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userInfo = JSON.parse(userInfo);
			}
		},
		// 获取老人信息
		getElderlyInfo() {
			const elderlyListData = uni.getStorageSync('elderlyList');
			if (elderlyListData) {
				console.log(this.elderlyId)
				const elderlyList = JSON.parse(elderlyListData);
				console.log(elderlyList);
				const elderly = elderlyList.find(item => item.id === this.elderlyId);
				console.log(elderly);
				if (elderly) {
					this.elderlyInfo = elderly;
				}
			}
		},
		// 格式化手机号
		formatPhone(phone) {
			if (!phone) return '未设置';
			if (phone.length === 11) {
				return phone.substring(0, 3) + '******' + phone.substring(9);
			}
			return phone;
		},
		// 格式化身份证号
		formatIdCard(idCardNumber) {
			if (!idCardNumber) return '未设置';
			if (idCardNumber.length === 18) {
				return idCardNumber.substring(0, 6) + '********' + idCardNumber.substring(14);
			}
			return idCardNumber;
		},
		// 编辑头像
		editAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];

					// 将图片转换为base64
					this.imageToBase64(tempFilePath, (base64) => {
						// 更新头像为base64字符串
						this.elderlyInfo.avatar = base64;

						uni.showToast({
							title: '头像更新成功',
							icon: 'success'
						});
					});
				}
			});
		},
		// 图片转base64
		imageToBase64(filePath, callback) {
			// 读取图片文件
			uni.getFileSystemManager().readFile({
				filePath: filePath,
				encoding: 'base64',
				success: (res) => {
					// 获取图片类型
					let imageType = 'image/jpeg'; // 默认类型
					if (filePath.endsWith('.png')) {
						imageType = 'image/png';
					} else if (filePath.endsWith('.gif')) {
						imageType = 'image/gif';
					}

					// 拼接完整的base64字符串
					const base64String = `data:${imageType};base64,${res.data}`;
					callback(base64String);
				},
				fail: (err) => {
					console.error('图片转base64失败', err);
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					});
				}
			});
		},
		// 编辑姓名
		editName() {
			this.editForm.name = this.elderlyInfo.name;
			this.$refs.namePopup.open();
		},
		// 编辑年龄
		editAge() {
			this.editForm.age = this.elderlyInfo.age;
			this.$refs.agePopup.open();
		},
		// 编辑性别
		editGender() {
			this.editForm.gender = this.elderlyInfo.gender;
			this.$refs.genderPopup.open();
		},
		// 性别选择变更
		genderChange(e) {
			this.editForm.gender = e.detail.value;
		},
		// 关系选择变更
		relationChange(e) {
			this.editForm.relation = e.detail.value;
		},
		// 编辑关系
		editRelation() {
			this.editForm.relation = this.elderlyInfo.relation;
			this.$refs.relationPopup.open();
		},
		// 编辑手机号
		editPhone() {
			this.editForm.phone = this.elderlyInfo.phone;
			this.$refs.phonePopup.open();
		},
		// 编辑身份证号
		editIdCard() {
			this.editForm.idCardNumber = this.elderlyInfo.idCardNumber;
			this.$refs.idCardPopup.open();
		},
		// 编辑住址
		editAddress() {
			this.editForm.homeAddress = this.elderlyInfo.homeAddress || '';
			this.$refs.addressPopup.open();
		},
		// 编辑健康状况
		editHealthStatus() {
			this.editForm.healthInfo = this.elderlyInfo.healthInfo || '';
			this.$refs.healthStatusPopup.open();
		},
		// 编辑紧急联系人
		editEmergencyContact() {
			this.editForm.emergencyContact = this.elderlyInfo.emergencyContact || '';
			this.$refs.emergencyContactPopup.open();
		},
		// 编辑紧急联系人电话
		editEmergencyPhone() {
			this.editForm.emergencyPhone = this.elderlyInfo.emergencyPhone || '';
			this.$refs.emergencyPhonePopup.open();
		},
		// 关闭弹窗
		closePopup(ref) {
			this.$refs[ref].close();
		},
		// 确认编辑
		confirmEdit(field) {
			// 验证输入
			if (field === 'name' && !this.editForm.name.trim()) {
				uni.showToast({
					title: '姓名不能为空',
					icon: 'none'
				});
				return;
			}

			if (field === 'age') {
				const age = parseInt(this.editForm.age);
				if (isNaN(age) || age <= 0 || age > 120) {
					uni.showToast({
						title: '请输入有效年龄',
						icon: 'none'
					});
					return;
				}
				this.elderlyInfo.age = age;
			} else if (field === 'idCard') {
				// 更新身份证号字段
				this.elderlyInfo.idCardNumber = this.editForm.idCardNumber;
			} else if (field === 'healthStatus') {
				// 更新健康状况字段
				this.elderlyInfo.healthInfo = this.editForm.healthInfo;
			} else if (field === 'address') {
				// 更新住址字段
				this.elderlyInfo.homeAddress = this.editForm.homeAddress;
			} else {
				// 更新对应字段
				this.elderlyInfo[field] = this.editForm[field];
			}

			// 关闭弹窗
			this.$refs[`${field}Popup`].close();

			uni.showToast({
				title: '更新成功',
				icon: 'success'
			});
		},
		// 保存老人信息
		saveElderlyInfo() {
			// 验证必填字段
			if (!this.elderlyInfo.name) {
				uni.showToast({
					title: '请填写姓名',
					icon: 'none'
				});
				return;
			}

			// 获取现有老人列表
			let elderlyList = [];
			let reqUrl = '';
			const elderlyListData = uni.getStorageSync('elderlyList');
			if (elderlyListData) {
				try {
					const parsedData = JSON.parse(elderlyListData);
					// 确保 elderlyList 是数组
					elderlyList = Array.isArray(parsedData) ? parsedData : [];
				} catch (e) {
					console.error('解析老人列表失败', e);
					// 如果解析失败，使用空数组
					elderlyList = [];
					// 重新初始化存储
					uni.setStorageSync('elderlyList', JSON.stringify([]));
				}
			}

			if (this.isAddMode) {
				// 添加模式：添加到列表
				this.elderlyInfo.relationId = this.userInfo.id;
				elderlyList.push(this.elderlyInfo);
				reqUrl = this.$backUrl + '/client/c/elderlyusers/add';
			} else {
				// 编辑模式：更新列表中的对应项
				reqUrl = this.$backUrl + '/client/c/elderlyusers/edit';
				const index = elderlyList.findIndex(item => item.id === this.elderlyInfo.id);
				if (index !== -1) {
					elderlyList[index] = this.elderlyInfo;
				} else if (this.elderlyInfo.id) {
					// 如果找不到对应项但有ID，添加到列表
					elderlyList.push(this.elderlyInfo);
				}
			}

			// 保存到本地存储
				uni.setStorageSync('elderlyList', JSON.stringify(elderlyList));

				uni.showToast({
					title: '保存成功',
					icon: 'success',
					success: () => {
						// 延迟返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				});

			// 保存到数据库
			uni.request({
				url: reqUrl,
				method: 'POST',
				header: {
					'token': uni.getStorageSync('token')
				},
				data: this.elderlyInfo,
				success: () => {
				},
				fail: (err) => {
					console.error('老人信息更新失败', err);
				}
			})
		},
		// 删除老人信息
		deleteElderly() {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除该老人信息吗？',
				success: (res) => {
					if (res.confirm) {
						// 获取现有老人列表
						let elderlyList = [];
						const elderlyListData = uni.getStorageSync('elderlyList');
						if (elderlyListData) {
							try {
								const parsedData = JSON.parse(elderlyListData);
								// 确保 elderlyList 是数组
								elderlyList = Array.isArray(parsedData) ? parsedData : [];
							} catch (e) {
								console.error('解析老人列表失败', e);
								// 如果解析失败，使用空数组
								elderlyList = [];
								// 重新初始化存储
								uni.setStorageSync('elderlyList', JSON.stringify([]));
							}
							// 过滤掉要删除的老人
							elderlyList = elderlyList.filter(item => item.id !== this.elderlyInfo.id);
							// 保存到本地存储
							uni.setStorageSync('elderlyList', JSON.stringify(elderlyList));

							uni.showToast({
								title: '删除成功',
								icon: 'success',
								success: () => {
									// 延迟返回上一页
									setTimeout(() => {
										uni.navigateBack();
									}, 1500);
								}
							});
						}
					}
				}
			});
		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 80px; /* 为底部按钮留出空间 */
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

.delete-btn {
	color: #ff4d4f;
	font-size: 14px;
}

.elderly-form {
	margin-top: 10px;
	background-color: white;
	border-radius: 8px;
	overflow: hidden;
}

.form-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
	border-bottom: none;
}

.item-label {
	font-size: 16px;
	color: #333;
}

.item-content {
	display: flex;
	align-items: center;
}

.item-value {
	font-size: 16px;
	color: #666;
	margin-right: 10px;
}

.avatar-image {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	margin-right: 10px;
	background-color: #f0f0f0;
}

.save-button {
	position: fixed;
	bottom: 20px;
	left: 20px;
	right: 20px;
	height: 50px;
	line-height: 50px;
	text-align: center;
	background-color: #c8a287;
	color: white;
	border-radius: 25px;
	font-size: 16px;
	font-weight: bold;
	box-shadow: 0 2px 10px rgba(200, 162, 135, 0.5);
}

/* 弹窗样式 */
.popup-content {
	background-color: white;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
	overflow: hidden;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.popup-title {
	font-size: 16px;
	font-weight: bold;
}

.popup-close {
	font-size: 14px;
	color: #666;
}

.popup-body {
	padding: 20px 15px;
}

.input-field {
	width: 100%;
	height: 40px;
	border-bottom: 1px solid #f0f0f0;
	font-size: 16px;
}

.textarea-field {
	width: 100%;
	height: 100px;
	border: 1px solid #f0f0f0;
	border-radius: 5px;
	padding: 10px;
	font-size: 16px;
}

.radio-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
	border-bottom: 1px solid #f0f0f0;
}

.radio-item:last-child {
	border-bottom: none;
}

.radio-label {
	font-size: 16px;
}

.popup-footer {
	padding: 15px;
}

.confirm-btn {
	width: 100%;
	height: 44px;
	line-height: 44px;
	background-color: #c8a287;
	color: white;
	border-radius: 5px;
	font-size: 16px;
}
</style>
