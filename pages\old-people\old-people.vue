<template>
	<view class="container">
		<!-- 导航栏 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<uni-icons type="arrow-left" size="24" color="#333"></uni-icons>
			</view>
			<view class="page-title">老人信息</view>
			<view style="width: 24px;"></view>
		</view> -->

		<!-- 老人列表 -->
		<view class="elderly-list">
			<view v-if="elderlyList.length > 0">
				<uni-swipe-action>
					<uni-swipe-action-item
						v-for="(item, index) in elderlyList"
						:key="index"
						:right-options="swipeOptions"
						@click="handleSwipeClick($event, item, index)"
					>
						<view
							class="elderly-item"
							@click="viewElderlyDetail(item.id)"
						>
							<view class="elderly-avatar">
								<image :src="item.avatar || '/static/images/avatar/avatar.svg'" class="avatar-image"></image>
							</view>
							<view class="elderly-info">
								<view class="elderly-name">{{ item.name }}</view>
								<view class="elderly-details">
									<text class="elderly-age">{{ item.age }}岁</text>
									<text class="elderly-gender">{{ item.gender }}</text>
								</view>
								<view class="elderly-relation">{{ item.relation }}</view>
							</view>
							<uni-icons type="right" size="18" color="#ccc"></uni-icons>
						</view>
					</uni-swipe-action-item>
				</uni-swipe-action>
			</view>

			<!-- 无数据提示 -->
			<view class="no-data" v-else>
				<image src="/static/images/no-data.png" class="no-data-image"></image>
				<text class="no-data-text">暂无老人信息</text>
			</view>
		</view>

		<!-- 添加按钮 -->
		<view class="add-button" @click="addElderly">
			<uni-icons type="plusempty" size="24" color="#fff"></uni-icons>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			elderlyList: [],
			userInfo: {},
			// 滑动操作选项
			swipeOptions: [
				{
					text: '删除',
					style: {
						backgroundColor: '#dd524d'
					}
				}
			]
		}
	},
	onLoad() {
		this.getUserInfo();
	},
	onShow() {
		// 每次显示页面时刷新列表
		this.getElderlyList();
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		getUserInfo() {
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userInfo = JSON.parse(userInfo);
			}
		},
		// 获取老人列表
		getElderlyList() {
			// 从本地存储获取老人列表
			/* const elderlyListData = uni.getStorageSync('elderlyList');
			console.log(elderlyListData);
			if (elderlyListData) {
				this.elderlyList = JSON.parse(elderlyListData);
				console.log(this.elderlyList);

			} else {
				// 获取远程数据
				uni.request({
					url: this.$backUrl + '/client/c/elderlyusers/page',
					method: 'GET',
					data: {
						current: 1,
						size: 10,
						searchKey: this.userInfo.id
					},
					header: {
						'token': uni.getStorageSync('token')
					},
					success: (res) => {
						this.elderlyList = res.data.data;
						console.log(this.elderlyList);
						// 保存到本地存储
						uni.setStorageSync('elderlyList', JSON.stringify(this.elderlyList));
					},
					fail: (err)=> {}
				})
			} */
				uni.request({
					url: this.$backUrl + '/client/c/elderlyusers/page',
					method: 'GET',
					data: {
						current: 1,
						size: 10,
						searchKey: this.userInfo.id
					},
					header: {
						'token': uni.getStorageSync('token')
					},
					success: (res) => {
						this.elderlyList = res.data.data.records;
						// 保存到本地存储
						uni.setStorageSync('elderlyList', JSON.stringify(this.elderlyList));
					},
					fail: () => {}
				})
		},
		// 查看老人详情
		viewElderlyDetail(id) {
			uni.navigateTo({
				url: `/pages/old-people/elderly-detail?id=${id}`
			});
		},
		// 添加老人
		addElderly() {
			uni.navigateTo({
				url: '/pages/old-people/elderly-detail?mode=add'
			});
		},
		// 处理滑动操作点击
		handleSwipeClick(e, item, index) {
			if (e.index === 0) { // 删除操作
				this.confirmDeleteElderly(item, index);
			}
		},
		// 确认删除老人
		confirmDeleteElderly(item, index) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除老人"${item.name}"的信息吗？`,
				success: (res) => {
					if (res.confirm) {
						this.deleteElderly(item, index);
					}
				}
			});
		},
		// 删除老人
		deleteElderly(item, index) {
			// 显示加载中
			uni.showLoading({
				title: '删除中...'
			});

			// 发送删除请求到服务器
			uni.request({
				url: this.$backUrl + '/client/c/elderlyusers/delete',
				method: 'POST',
				header: {
					'token': uni.getStorageSync('token')
				},
				data: [{
					id: item.id
				}],
				success: () => {
					// 从列表中移除
					this.elderlyList.splice(index, 1);

					// 更新本地存储
					uni.setStorageSync('elderlyList', JSON.stringify(this.elderlyList));

					uni.hideLoading();
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				},
				fail: () => {
					uni.hideLoading();
					uni.showToast({
						title: '删除失败',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 80px; /* 为底部按钮留出空间 */
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

.elderly-list {
	margin-top: 10px;
	padding: 0 15px;
}

/* 滑动操作容器样式 */
.elderly-list /deep/ .uni-swipe-action-item {
	margin-bottom: 10px;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.elderly-item {
	display: flex;
	align-items: center;
	padding: 15px;
	background-color: white;
	border-radius: 8px;
}

.elderly-avatar {
	margin-right: 15px;
}

.avatar-image {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	background-color: #f0f0f0;
}

.elderly-info {
	flex: 1;
}

.elderly-name {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 5px;
}

.elderly-details {
	display: flex;
	margin-bottom: 5px;
}

.elderly-age {
	font-size: 14px;
	color: #666;
	margin-right: 10px;
}

.elderly-gender {
	font-size: 14px;
	color: #666;
}

.elderly-relation {
	font-size: 14px;
	color: #c8a287;
	background-color: rgba(200, 162, 135, 0.1);
	padding: 2px 8px;
	border-radius: 10px;
	display: inline-block;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}

.add-button {
	position: fixed;
	bottom: 30px;
	right: 30px;
	width: 60px;
	height: 60px;
	background-color: #c8a287;
	border-radius: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 10px rgba(200, 162, 135, 0.5);
}
</style>
