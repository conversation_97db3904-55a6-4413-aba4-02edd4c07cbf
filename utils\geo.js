import * as geolib from 'geolib'

// 格式化距离显示
export function formatDistance(meters) {
  if (meters === undefined || meters === null) return '未知距离';
  return meters < 1000 
    ? `${meters}米` 
    : `${(meters / 1000).toFixed(1)}公里`;
}

// 计算两点之间的距离
export function getDistance(point1, point2) {
    console.log(point1, point2);
  return geolib.getDistance(point1, point2);
}

// 获取最近的点
export function getNearestPoint(userLocation, points) {
  return geolib.findNearest(userLocation, points);
}

// 判断点是否在区域内
export function isPointInArea(point, area) {
  return geolib.isPointInPolygon(point, area);
}

export default {
  formatDistance,
  getDistance,
  getNearestPoint,
  isPointInArea,
  // 导出所有geolib方法
  ...geolib
}