<template>
	<view class="container">
		<!-- 活动封面图 -->
		<view class="cover-container">
			<image :src="activityInfo.serimg || defaultImage" mode="aspectFill" class="cover-image"></image>
			<!-- <view class="back-button" @click="goBack">
				<my-icon type="arrow-left" size="20" color="#fff"></my-icon>
			</view> -->
		</view>
		
		<!-- 活动标题和状态 -->
		<view class="title-container">
			<view class="activity-title">{{activityInfo.sername || '活动加载中...'}}</view>
			<!-- <view class="activity-status" :class="getStatusClass(activityInfo.status)" v-if="activityInfo.status !== undefined">
				{{getStatusText(activityInfo.status)}}
			</view> -->
		</view>
		
		<!-- 活动信息卡片 -->
		<view class="info-card">
			<view class="info-item">
				<view class="info-icon">
					<my-icon type="clock" size="20" color="#BE957E"></my-icon>
				</view>
				<view class="info-content">
					<view class="info-label">活动时间</view>
					<view class="info-value">{{activityInfo.seractivitydate || '待定'}}</view>
				</view>
			</view>
			
			<view class="info-divider"></view>

			<view class="info-item">
				<view class="info-icon">
					<my-icon type="clock" size="20" color="#BE957E"></my-icon>
				</view>
				<view class="info-content">
					<view class="info-label">截止报名时间</view>
					<view class="info-value">{{activityInfo.seractivityenddate || '待定'}}</view>
				</view>
			</view>
			
			<!-- <view class="info-item">
				<view class="info-icon">
					<my-icon type="location" size="20" color="#BE957E"></my-icon>
				</view>
				<view class="info-content">
					<view class="info-label">活动地点</view>
					<view class="info-value">{{activityInfo.location || '宝山区文化中心'}}</view>
				</view>
			</view> -->
			
			<view class="info-divider"></view>
			
			<view class="info-item">
				<view class="info-icon">
					<my-icon type="people" size="20" color="#BE957E"></my-icon>
				</view>
				<view class="info-content">
					<view class="info-label">已报名人数</view>
					<view class="info-value">
						<text class="current-people">{{activityInfo.serparticipatenum || 0}}</text>
						/<text class="max-people">{{activityInfo.serenrollmaxnum || 50}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 活动详情 -->
		<view class="detail-section">
			<view class="section-title">活动详情</view>
			<!-- #ifdef MP-WEIXIN -->
			<!-- <view @click="playTest"><my-icon type="play" size="24"></my-icon></view> -->
			<!-- #endif -->
			<rich-text :nodes="activityInfo.seractivityinfo || '暂无详细介绍'"></rich-text>
			
			<!-- <view class="detail-images" v-if="detailImages.length > 0">
				<image 
					v-for="(img, index) in detailImages" 
					:key="index" 
					:src="img" 
					mode="widthFix" 
					class="detail-image"
					@click="previewImage(index)"
				></image>
			</view> -->
		</view>
		
		<!-- 活动须知 -->
		<!-- <view class="notice-section">
			<view class="section-title">活动须知</view>
			<view class="notice-items">
				<view class="notice-item">
					<view class="notice-dot"></view>
					<text>请提前10分钟到达活动现场</text>
				</view>
				<view class="notice-item">
					<view class="notice-dot"></view>
					<text>活动期间请听从工作人员安排</text>
				</view>
				<view class="notice-item">
					<view class="notice-dot"></view>
					<text>活动地点可能有阶梯，请注意安全</text>
				</view>
				<view class="notice-item">
					<view class="notice-dot"></view>
					<text>如有特殊需求，请提前联系工作人员</text>
				</view>
			</view>
		</view> -->
		
		<!-- 底部报名按钮 -->
		<!-- <view class="footer">
			<button 
				class="register-button" 
				:class="{'disabled': activityInfo.status !== 0}"
				@click="registerActivity"
			>
				{{getButtonText(activityInfo.status)}}
			</button>
		</view> -->
	</view>
</template>

<script>
export default {
	data() {
		return {
			activityId: '',
			defaultImage: 'https://images.unsplash.com/photo-1493676304819-0d7a8d026dcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
			activityInfo: {},
			detailImages: [
				'https://images.unsplash.com/photo-1543276228-3e3f6175f119?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
				'https://images.unsplash.com/photo-1536749395678-26eee1f3f8a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
				'https://images.unsplash.com/photo-1543339488-0652ab868aab?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
			]
		}
	},
	onLoad(options) {
		this.activityId = options.id;
		this.getActivityDetail();
	},
	methods: {
		getActivityDetail() {
			uni.showLoading({
				title: '加载中'
			});
			
			// 根据ID从接口获取活动详情
			uni.request({
				url: this.$backUrl + `/biz/syseventregistration/detail-out?id=${this.activityId}`,
				success: (res) => {
					if (res.data.data) {
						// 添加演示用的数据
						this.activityInfo = {
							...res.data.data
						};
					} else {
						uni.showToast({
							title: '获取活动详情失败',
							icon: 'none'
						});
					}
				},
				fail: (err) => {
					console.error('获取活动详情失败', err);
					uni.showToast({
						title: '获取活动详情失败',
						icon: 'none'
					});
				},
				complete: () => {
					uni.hideLoading();
				}
			});
		},
		goBack() {
			uni.navigateBack();
		},
		getStatusClass(status) {
			switch(status) {
				case 0: return 'status-active';
				case 1: return 'status-full';
				case 2: return 'status-ended';
				default: return 'status-active';
			}
		},
		getStatusText(status) {
			switch(status) {
				case 0: return '报名中';
				case 1: return '已满';
				case 2: return '已结束';
				default: return '报名中';
			}
		},
		getButtonText(status) {
			switch(status) {
				case 0: return '立即报名';
				case 1: return '名额已满';
				case 2: return '活动已结束';
				default: return '立即报名';
			}
		},
		registerActivity() {
			if (this.activityInfo.status !== 0) {
				return;
			}
			
			uni.showModal({
				title: '确认报名',
				content: `确定要报名参加"${this.activityInfo.sername}"活动吗？`,
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '报名中'
						});
						
						// 模拟报名请求
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '报名成功',
								icon: 'success'
							});
							
							// 更新当前人数
							this.activityInfo.currentPeople++;
							
							// 如果人数已满，更新状态
							if (this.activityInfo.currentPeople >= this.activityInfo.maxPeople) {
								this.activityInfo.status = 1;
							}
						}, 1500);
					}
				}
			});
		},
		previewImage(index) {
			uni.previewImage({
				current: index,
				urls: this.detailImages
			});
		},
		// #ifdef MP-WEIXIN
		playTest() {
			console.log('插件测试')
			const plugin = requirePlugin("WechatSI")
			plugin.textToSpeech({
			    lang: "zh_CN",
			    tts: true,
			    content: this.activityInfo.seractivityinfo,
			    success: function(res) {
			        console.log("succ tts", res.filename)
					const innerAudioContext = uni.createInnerAudioContext();
					innerAudioContext.autoplay = true;
					innerAudioContext.src = res.filename;
					innerAudioContext.onPlay(() => {
					  console.log('开始播放');
					});
					innerAudioContext.onError((res) => {
					  console.log(res.errMsg);
					  console.log(res.errCode);
					});
			    },
			    fail: function(res) {
			        console.log("fail tts", res)
			    }
			})
		}
		// #endif
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 80px;
}

.cover-container {
	position: relative;
	height: 250px;
}

.cover-image {
	width: 100%;
	height: 100%;
}

.back-button {
	position: absolute;
	top: 40px;
	left: 15px;
	width: 36px;
	height: 36px;
	background-color: rgba(0, 0, 0, 0.3);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.title-container {
	padding: 15px;
	background-color: #fff;
	margin-bottom: 10px;
}

.activity-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	padding-right: 80px;
}

.activity-status {
	position: absolute;
	top: 270px;
	right: 15px;
	padding: 3px 12px;
	border-radius: 15px;
	font-size: 12px;
}

.status-active {
	background-color: #ecf8f3;
	color: #4caf50;
}

.status-full {
	background-color: #fff7e6;
	color: #ff9800;
}

.status-ended {
	background-color: #f5f5f5;
	color: #9e9e9e;
}

.info-card {
	background-color: #fff;
	padding: 15px;
	border-radius: 8px;
	margin: 0 15px 15px;
}

.info-item {
	display: flex;
	align-items: center;
	padding: 10px 0;
}

.info-icon {
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #fef6ee;
	border-radius: 50%;
	margin-right: 15px;
}

.info-content {
	flex: 1;
}

.info-label {
	font-size: 14px;
	color: #999;
	margin-bottom: 5px;
}

.info-value {
	font-size: 16px;
	color: #333;
}

.info-divider {
	height: 1px;
	background-color: #f0f0f0;
	margin: 5px 0;
}

.current-people {
	color: #BE957E;
	font-weight: bold;
}

.detail-section, .notice-section {
	background-color: #fff;
	padding: 15px;
	margin-bottom: 15px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
	position: relative;
	padding-left: 10px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 2px;
	height: 16px;
	width: 3px;
	background-color: #BE957E;
}

.detail-images {
	display: flex;
	flex-wrap: wrap;
	margin-top: 15px;
}

.detail-image {
	width: calc(33.33% - 6px);
	margin: 3px;
	border-radius: 4px;
}

.notice-items {
	padding: 0 5px;
}

.notice-item {
	display: flex;
	margin-bottom: 10px;
	font-size: 14px;
	color: #666;
}

.notice-dot {
	width: 6px;
	height: 6px;
	border-radius: 50%;
	background-color: #BE957E;
	margin: 8px 10px 0 0;
	flex-shrink: 0;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.register-button {
	background-color: #BE957E;
	color: #fff;
	border: none;
	border-radius: 25px;
	font-size: 16px;
	font-weight: bold;
}

.register-button.disabled {
	background-color: #ccc;
}
</style>