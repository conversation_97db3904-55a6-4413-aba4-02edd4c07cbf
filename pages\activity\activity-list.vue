<template>
	<view class="container">
		<!-- 活动列表页面头部 -->
		<view class="header">
			<view class="title">精彩活动</view>
			<view class="subtitle">为老年人提供丰富多彩的活动</view>
		</view>

		<!-- 活动分类标签 -->
		<view class="category-tabs">
			<view
				v-for="(tab, index) in categories"
				:key="index"
				:class="['category-tab', currentTab === index ? 'active' : '']"
				@click="changeTab(index)"
			>
				{{tab.name}}
			</view>
		</view>

		<!-- 活动列表 -->
		<view class="activity-list">
			<view
				class="activity-card"
				v-for="(activity, index) in filteredActivities"
				:key="index"
				@click="goToDetail(activity.id)"
			>
				<image :src="activity.serimg || defaultImage" mode="aspectFill" class="activity-image"></image>
				<view class="activity-info">
					<view class="activity-title">{{activity.sername}}</view>
					<view class="activity-meta">
						<view class="activity-time">
							<my-icon type="time" size="16" color="#BE957E" class="icon-mr"></my-icon>
							<text>{{activity.seractivitydate}}</text>
						</view>
						<!-- <view class="activity-location">
							<my-icon type="location" size="16" color="#BE957E" class="icon-mr"></my-icon>
							<text>{{activity.location || '宝山区文化中心'}}</text>
						</view> -->
					</view>
					<!-- <view class="activity-status" :class="getStatusClass(activity.status)">
						{{getStatusText(activity.status)}}
					</view> -->
				</view>
			</view>

			<!-- 无数据提示 -->
			<view class="no-data" v-if="filteredActivities.length === 0">
				<image src="/static/images/no-data.png" class="no-data-image"></image>
				<text class="no-data-text">暂无相关活动</text>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMoreActivities && filteredActivities.length > 0" class="load-more" @click="loadMoreActivities">
				加载更多
			</view>
			<view v-else-if="filteredActivities.length > 0" class="no-more">
				没有更多活动了
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			defaultImage: 'https://images.unsplash.com/photo-1493676304819-0d7a8d026dcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
			currentTab: 0,
			categories: [
				{ name: '全部', id: 'all' },
				{ name: '文艺活动', id: 'art' },
				{ name: '健康讲座', id: 'health' },
				{ name: '社区活动', id: 'community' }
			],
			activityList: [],
			page: 1,
			pageSize: 10,
			hasMoreActivities: true
		}
	},
	computed: {
		filteredActivities() {
			if (this.currentTab === 0) {
				return this.activityList;
			} else {
				return this.activityList.filter(item => item.category === this.categories[this.currentTab].id);
			}
		}
	},
	onLoad() {
		this.loadMoreActivities();
	},
	methods: {
		// 已删除原来的getActivityList方法，改用loadMoreActivities方法
		loadMoreActivities() {
			if (this.hasMoreActivities) {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});

				// 使用分页参数current和size
				uni.request({
					//url: 'https://bsyltest.shmallshow.com/biz/syseventregistration/listAll',
					url: this.$backUrl + '/biz/syseventregistration/page-out',
					data: {
						current: this.page,  // 当前页码
						size: this.pageSize  // 每页数据量
					},
					header: {},
					success: (res) => {
						console.log(res.data);
						if (res.data && res.data.data) {
							// 处理数据，添加额外属性
							const newActivities = res.data.data.records.map(item => {
								return {
									...item,
									// 随机分配一个类别用于演示
									category: this.categories[Math.floor(Math.random() * (this.categories.length - 1) + 1)].id,
									// 随机分配一个状态：0-报名中，1-已满，2-已结束
									status: Math.floor(Math.random() * 3)
								}
							});

							if (this.page === 1) {
								// 第一页，直接替换数据
								this.activityList = newActivities;
							} else {
								// 不是第一页，追加数据
								this.activityList = [...this.activityList, ...newActivities];
							}

							// 判断是否还有更多数据
							if (newActivities.length < this.pageSize) {
								this.hasMoreActivities = false;
							} else {
								// 页码加1，准备下次加载
								this.page++;
								this.hasMoreActivities = true;
							}
						} else {
							// 没有返回数据，设置没有更多
							this.hasMoreActivities = false;
						}
					},
					fail: (err) => {
						console.error('获取活动列表失败', err);
						uni.showToast({
							title: '获取活动列表失败',
							icon: 'none'
						});
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			}
		},
		changeTab(index) {
			if (this.currentTab !== index) {
				this.currentTab = index;

				// 如果是切换到全部标签，不需要重新加载
				// 如果是切换到其他标签，则使用过滤器处理
				// 注意：这里我们不重新请求数据，而是使用计算属性过滤现有数据
				// 如果需要每次切换标签都重新请求数据，可以在这里重置分页并调用loadMoreActivities
			}
		},
		goToDetail(id) {
			uni.navigateTo({
				url: `/pages/activity/activity-detail?id=${id}`
			});
		},
		getStatusClass(status) {
			switch(status) {
				case 0: return 'status-active';
				case 1: return 'status-full';
				case 2: return 'status-ended';
				default: return 'status-active';
			}
		},
		getStatusText(status) {
			switch(status) {
				case 0: return '报名中';
				case 1: return '已满';
				case 2: return '已结束';
				default: return '报名中';
			}
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.header {
	margin-bottom: 20px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.subtitle {
	font-size: 14px;
	color: #999;
	margin-top: 5px;
}

/* 分类标签 */
.category-tabs {
	display: flex;
	overflow-x: auto;
	background-color: white;
	padding: 10px 15px;
	margin-bottom: 20px;
}

.category-tab {
	padding: 6px 12px;
	margin-right: 10px;
	border-radius: 16px;
	font-size: 14px;
	white-space: nowrap;
	background-color: #f5f5f5;
	color: #666;
}

.category-tab.active {
	background-color: #C8A287;
	color: white;
}

.header {
	padding: 0 15px;
}

.activity-list {
	padding: 0 15px;
}

.activity-card {
	background-color: #fff;
	border-radius: 12px;
	margin-bottom: 15px;
	overflow: hidden;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.activity-image {
	width: 100%;
	height: 160px;
}

.activity-info {
	padding: 15px;
	position: relative;
}

.activity-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
	color: #333;
}

.activity-meta {
	font-size: 14px;
	color: #666;
}

.activity-time, .activity-location {
	display: flex;
	align-items: center;
	margin-bottom: 5px;
}

.icon-mr {
	margin-right: 5px;
}

.activity-status {
	position: absolute;
	top: 15px;
	right: 15px;
	padding: 3px 10px;
	border-radius: 12px;
	font-size: 12px;
}

.status-active {
	background-color: #ecf8f3;
	color: #4caf50;
}

.status-full {
	background-color: #fff7e6;
	color: #ff9800;
}

.status-ended {
	background-color: #f5f5f5;
	color: #9e9e9e;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}

.load-more, .no-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #999;
}

.load-more {
	color: #BE957E;
}
</style>