import App from './App'
import geoUtils from './utils/geo'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
if(process.env.NODE_ENV === 'development') {
	Vue.prototype.$backUrl = 'http://*************:82'
} else {
	Vue.prototype.$backUrl = 'https://bsyltest.shmallshow.com/api'
}

// 添加geoUtils到Vue
Vue.prototype.$geo = geoUtils

App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  if(process.env.NODE_ENV === 'development') {
    app.config.globalProperties.$backUrl = 'http://*************:82'
  } else {
    app.config.globalProperties.$backUrl = 'https://bsyltest.shmallshow.com/api'
  }

  app.config.globalProperties.$geo = geoUtils

  return {
    app
  }
}
// #endif