<template>
	<view class="container">
		<!-- 资讯封面图 -->
		<view class="cover-container">
			<image :src="newsInfo.coverImage || defaultImage" mode="aspectFill" class="cover-image"></image>
		</view>

		<!-- 资讯标题、发布时间和分类 -->
		<view class="title-container">
			<view class="news-title">{{newsInfo.title || '资讯加载中...'}}</view>
			<view class="news-meta">
				<view class="news-time">
					<my-icon type="clock" size="16" color="#999" class="icon-mr"></my-icon>
					<text>{{newsInfo.publishDate || '待定'}}</text>
				</view>
				<view class="news-category" v-if="newsInfo.category">
					{{getCategoryName(newsInfo.category)}}
				</view>
			</view>
		</view>

		<!-- 资讯详情 -->
		<view class="detail-section">
			<view class="section-title">详细内容</view>
			<rich-text :nodes="newsInfo.content || '暂无详细内容'"></rich-text>

			<!-- <view class="detail-images" v-if="newsInfo.images && newsInfo.images.length > 0">
				<image
					v-for="(img, index) in newsInfo.images"
					:key="index"
					:src="img"
					mode="widthFix"
					class="detail-image"
					@click="previewImage(index)"
				></image>
			</view> -->
		</view>

		<!-- 相关资讯 -->
		<!-- <view class="related-section" v-if="relatedNews.length > 0">
			<view class="section-title">相关资讯</view>
			<view class="related-list">
				<view
					class="related-item"
					v-for="(item, index) in relatedNews"
					:key="index"
					@click="goToDetail(item.id)"
				>
					<image :src="item.coverImage" mode="aspectFill" class="related-image"></image>
					<view class="related-title">{{item.title}}</view>
				</view>
			</view>
		</view> -->

		<!-- 底部分享按钮 -->
		<view class="footer">
			<button class="share-button" open-type="share">
				<my-icon type="share" size="20" color="#fff" class="icon-mr"></my-icon>
				分享资讯
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			newsId: '',
			defaultImage: 'https://images.unsplash.com/photo-1493676304819-0d7a8d026dcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
			newsInfo: {},
			relatedNews: [],
			categories: [
				{ name: '全部', id: 'all' },
				{ name: '医养', id: 'medical' },
				{ name: '康养', id: 'health' },
				{ name: '悦养', id: 'joy' }
			]
		}
	},
	onLoad(options) {
		this.newsId = options.id;
		this.getNewsDetail();
	},
	// 分享功能
	onShareAppMessage() {
		return {
			title: this.newsInfo.title || '养老资讯分享',
			path: `/pages/elderly-news/news-detail?id=${this.newsId}`,
			imageUrl: this.newsInfo.coverImage || this.defaultImage
		}
	},
	methods: {
		getNewsDetail() {
			uni.showLoading({
				title: '加载中'
			});

			// 模拟从服务器获取数据
			setTimeout(() => {
				// 解析ID获取模拟数据
				const idParts = this.newsId.split('-');
				const page = parseInt(idParts[1]);
				const index = parseInt(idParts[2]);

				// 模拟资讯详情数据
				this.newsInfo = this.generateMockNewsDetail(page, index);

				// 模拟相关资讯
				this.relatedNews = this.generateMockRelatedNews();

				uni.hideLoading();
			}, 1000);
		},
		generateMockNewsDetail(page, index) {
			const categoryIds = ['medical', 'health', 'joy'];
			const titles = [
				'老年人健康饮食指南：这些食物助您延年益寿',
				'居家养老新模式：智能设备让生活更便捷',
				'老年人心理健康：如何保持积极心态',
				'养老保险政策解读：这些福利您知道吗',
				'老年人运动指南：适合银发族的健身方式',
				'防骗指南：老年人如何防范电信诈骗',
				'老年人用药安全：这些注意事项不可忽视',
				'老年人社交活动：丰富晚年生活的方式',
				'老年人营养补充：维生素与矿物质的重要性',
				'老年人居家安全：防跌倒措施全指南'
			];

			const idx = (page * 10 + index) % titles.length;
			const categoryIndex = idx % 3;

			return {
				id: this.newsId,
				title: titles[idx],
				coverImage: `https://picsum.photos/id/${70 + idx}/800/400`,
				publishDate: `2023-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 28) + 1}`,
				category: categoryIds[categoryIndex],
				viewCount: Math.floor(Math.random() * 1000) + 100,
				source: '老灵光养老平台',
				content: this.generateMockContent(idx),
				images: [
					`https://picsum.photos/id/${100 + idx}/800/600`,
					`https://picsum.photos/id/${110 + idx}/800/600`,
					`https://picsum.photos/id/${120 + idx}/800/600`
				]
			};
		},
		generateMockContent(index) {
			const contents = [
				'<p style="text-indent:2em;margin-bottom:15px;">随着年龄的增长，老年人的饮食需求也在发生变化。合理的饮食结构不仅能满足身体所需的营养，还能预防疾病，延缓衰老。本文将为您介绍适合老年人的健康饮食指南。</p><p style="text-indent:2em;margin-bottom:15px;">首先，老年人应当增加蛋白质的摄入。蛋白质是维持肌肉质量和免疫功能的重要营养素。优质的蛋白质来源包括瘦肉、鱼类、蛋类、豆制品等。每天应保证摄入足够的蛋白质，以维持身体功能。</p><p style="text-indent:2em;margin-bottom:15px;">其次，多摄入富含钙质的食物。随着年龄增长，骨质疏松的风险增加，因此补充钙质尤为重要。牛奶、酸奶、豆腐、深绿色蔬菜等都是良好的钙源。</p><p style="text-indent:2em;margin-bottom:15px;">第三，保证充足的水分摄入。老年人往往感觉不到口渴，容易出现脱水情况。每天应当有意识地喝水，保持身体水分平衡。</p><p style="text-indent:2em;margin-bottom:15px;">此外，老年人应当减少盐分和油脂的摄入，以预防高血压和心脑血管疾病。增加膳食纤维的摄入，有助于肠道健康和预防便秘。</p><p style="text-indent:2em;margin-bottom:15px;">最后，老年人应当根据自身情况适当补充维生素和矿物质。如维生素D有助于钙质吸收，维生素B12对神经系统健康至关重要。</p>',
				'<p style="text-indent:2em;margin-bottom:15px;">随着科技的发展，智能设备正在改变老年人的居家养老模式。这些设备不仅提高了老年人的生活质量，还增强了他们的安全感和独立性。</p><p style="text-indent:2em;margin-bottom:15px;">智能穿戴设备是老年人居家养老的得力助手。例如，智能手表可以监测心率、血压、睡眠质量等健康指标，还能实现一键呼叫紧急联系人的功能。</p><p style="text-indent:2em;margin-bottom:15px;">智能家居系统也为老年人提供了便利。通过语音控制，老年人可以轻松调节室内温度、开关灯具、控制电器等，减少了不必要的移动和操作难度。</p><p style="text-indent:2em;margin-bottom:15px;">远程医疗设备让老年人足不出户就能获得医疗服务。通过视频通话，老年人可以与医生进行远程问诊，获取专业的健康建议。</p><p style="text-indent:2em;margin-bottom:15px;">智能药盒能够提醒老年人按时服药，避免漏服或重复服药的情况发生。有些智能药盒还能与手机应用连接，让家人远程监控老人的服药情况。</p><p style="text-indent:2em;margin-bottom:15px;">此外，智能安防系统能够实时监控家中情况，一旦发生异常，如跌倒、长时间不动等，系统会自动报警并通知家人。</p>',
				'<p style="text-indent:2em;margin-bottom:15px;">心理健康对老年人的生活质量有着重要影响。随着年龄增长，老年人面临着退休、身体机能下降、社交圈缩小等变化，容易产生孤独、焦虑、抑郁等负面情绪。本文将介绍如何帮助老年人保持积极心态。</p><p style="text-indent:2em;margin-bottom:15px;">首先，保持社交活动是维护心理健康的重要方式。老年人可以参加社区活动、老年大学、兴趣小组等，与志同道合的朋友交流互动，减少孤独感。</p><p style="text-indent:2em;margin-bottom:15px;">其次，培养兴趣爱好能够丰富老年人的精神生活。绘画、书法、园艺、音乐等活动不仅能够带来乐趣，还能锻炼大脑，延缓认知功能衰退。</p><p style="text-indent:2em;margin-bottom:15px;">适当的体育锻炼也有助于改善心理状态。研究表明，运动能够促进大脑释放内啡肽，减轻焦虑和抑郁症状。太极拳、健步走、游泳等低强度运动适合老年人。</p><p style="text-indent:2em;margin-bottom:15px;">此外，老年人应当学会接受变化，调整心态。人生不同阶段有不同的任务和乐趣，退休后可以将精力投入到以前没有时间做的事情上，如旅游、陪伴家人等。</p><p style="text-indent:2em;margin-bottom:15px;">最后，如果老年人出现明显的情绪问题，如持续的抑郁、焦虑等，应当及时寻求专业心理咨询或医疗帮助。</p>'
			];

			return contents[index % contents.length];
		},
		generateMockRelatedNews() {
			const result = [];
			const titles = [
				'老年人健康饮食指南：这些食物助您延年益寿',
				'居家养老新模式：智能设备让生活更便捷',
				'老年人心理健康：如何保持积极心态',
				'养老保险政策解读：这些福利您知道吗'
			];

			for (let i = 0; i < 3; i++) {
				result.push({
					id: `news-0-${i}`,
					title: titles[i],
					coverImage: `https://picsum.photos/id/${200 + i}/200/200`
				});
			}

			return result;
		},
		getCategoryName(categoryId) {
			const category = this.categories.find(item => item.id === categoryId);
			return category ? category.name : '其他';
		},
		previewImage(index) {
			uni.previewImage({
				current: index,
				urls: this.newsInfo.images
			});
		},
		goToDetail(id) {
			// 如果点击的是当前资讯，不做处理
			if (id === this.newsId) return;

			uni.navigateTo({
				url: `/pages/elderly-news/news-detail?id=${id}`
			});
		}
	}
}
</script>

<style>
.container {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 80px;
}

.cover-container {
	position: relative;
	height: 250px;
}

.cover-image {
	width: 100%;
	height: 100%;
}

.title-container {
	padding: 15px;
	background-color: #fff;
	margin-bottom: 10px;
	position: relative;
}

.news-title {
	font-size: 20px;
	font-weight: bold;
	color: #333;
	margin-bottom: 10px;
}

.news-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 5px;
}

.news-time {
	font-size: 14px;
	color: #999;
	display: flex;
	align-items: center;
}

.news-category {
	display: inline-block;
	padding: 3px 10px;
	background-color: #FEF6EE;
	color: #BE957E;
	border-radius: 15px;
	font-size: 12px;
}

.detail-section, .related-section {
	background-color: #fff;
	padding: 15px;
	margin-bottom: 15px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
	position: relative;
	padding-left: 10px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 2px;
	height: 16px;
	width: 3px;
	background-color: #BE957E;
}

.detail-images {
	display: flex;
	flex-wrap: wrap;
	margin-top: 15px;
}

.detail-image {
	width: calc(33.33% - 6px);
	margin: 3px;
	border-radius: 4px;
}

.related-list {
	display: flex;
	overflow-x: scroll;
	padding: 5px 0;
}

.related-item {
	width: 200px;
	margin-right: 15px;
	flex-shrink: 0;
}

.related-image {
	width: 100%;
	height: 120px;
	border-radius: 8px;
	margin-bottom: 8px;
}

.related-title {
	font-size: 14px;
	color: #333;
	line-height: 1.4;
	/* 文本超出两行显示省略号 */
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.share-button {
	background-color: #BE957E;
	color: #fff;
	border: none;
	border-radius: 25px;
	font-size: 16px;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-mr {
	margin-right: 5px;
}
</style>
