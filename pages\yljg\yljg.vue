<template>
	<view class="container">
		<!-- 状态栏 -->
		<!-- <view class="status-bar">
			<view>{{currentTime}}</view>
			<view>
				<text class="iconfont icon-signal"></text>
				<text class="iconfont icon-wifi ml-2"></text>
				<text class="iconfont icon-battery-full ml-2"></text>
			</view>
		</view> -->

		<!-- 导航头部 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<text class="iconfont icon-arrow-left"></text>
			</view>
			<view class="page-title">用餐服务</view>
			<view>
				<text class="iconfont icon-ellipsis-h"></text>
			</view>
		</view> -->

		<!-- Banner -->
		<view class="banner">
			<image src="https://kodo.dingsd115.com/bs-uniapp/banner-yljg.png" alt="养老机构" class="w-full h-full"></image>
			<!-- <view class="banner-overlay">
				<view class="banner-title">养老机构</view>
			</view> -->
		</view>

		<!-- 筛选条件 -->
		<view class="filter-container">
			<!-- 机构类型 -->
			<view class="filter-item" @click="showFilterPopup('type')">
				<text class="filter-text">{{ activeTypeText }}</text>
				<uni-icons type="bottom" size="12" color="#666"></uni-icons>
			</view>

			<!-- 服务标签 -->
			<!-- <view class="filter-item" @click="showFilterPopup('service')">
				<text class="filter-text">{{ activeServiceText }}</text>
				<uni-icons type="bottom" size="12" color="#666"></uni-icons>
			</view> -->

			<!-- 地区 -->
			<view class="filter-item" @click="showFilterPopup('area')">
				<text class="filter-text">{{ activeAreaText }}</text>
				<uni-icons type="bottom" size="12" color="#666"></uni-icons>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<uni-popup ref="filterPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">{{ popupTitle }}</view>
					<view class="popup-close" @click="closeFilterPopup">取消</view>
				</view>

				<!-- 机构类型选项 -->
				<view class="popup-body" v-if="currentFilterType === 'type'">
					<view
						class="filter-option"
						v-for="(item, index) in typeOptions"
						:key="index"
						:class="{ active: tempType === item.value }"
						@click="selectFilterOption('type', item.value, item.name)"
					>
						{{ item.name }}
						<uni-icons v-if="tempType === item.value" type="checkmarkempty" size="18" color="#c8a287"></uni-icons>
					</view>
				</view>

				<!-- 服务标签选项 -->
				<view class="popup-body" v-if="currentFilterType === 'service'">
					<view
						class="filter-option"
						v-for="(item, index) in serviceOptions"
						:key="index"
						:class="{ active: tempService === item.value }"
						@click="selectFilterOption('service', item.value, item.name)"
					>
						{{ item.name }}
						<uni-icons v-if="tempService === item.value" type="checkmarkempty" size="18" color="#c8a287"></uni-icons>
					</view>
				</view>

				<!-- 地区选项 -->
				<view class="popup-body" v-if="currentFilterType === 'area'">
					<view
						class="filter-option"
						v-for="(item, index) in areaOptions"
						:key="index"
						:class="{ active: tempArea === item.value }"
						@click="selectFilterOption('area', item.value, item.name)"
					>
						{{ item.name }}
						<uni-icons v-if="tempArea === item.value" type="checkmarkempty" size="18" color="#c8a287"></uni-icons>
					</view>
				</view>

				<view class="popup-footer">
					<button class="reset-btn" @click="resetFilter">重置</button>
					<button class="confirm-btn" @click="confirmFilter">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 机构列表 -->
		<view class="restaurant-list">
			<view class="restaurant-item" v-for="(restaurant, index) in restaurants" :key="index" @click="viewInstitutionDetail(restaurant.id)">
				<view class="restaurant-image">
					<image :src="restaurant.image" :alt="restaurant.name" class="w-full h-full"></image>
				</view>
				<view class="restaurant-info">
					<view class="restaurant-header">
						<view class="restaurant-name">{{restaurant.name}}</view>
						<!-- <view class="restaurant-rating">
							<text class="iconfont icon-star"></text>
							<text>{{restaurant.rating}}</text>
						</view> -->
					</view>
					<view class="restaurant-details">
						<!-- <view>{{restaurant.price}}</view>
						<view>{{restaurant.distance}}</view> -->
						<view>{{restaurant.time}}</view>
						<view v-if="hasLocation">{{restaurant.distanceText}}</view>
					</view>
					<!-- <view class="restaurant-tags">
						<view class="restaurant-tag" v-for="(tag, tagIndex) in restaurant.tags" :key="tagIndex">
							{{tag}}
						</view>
					</view> -->
					<!-- <view class="restaurant-actions">
						<view class="action-button">
							<text class="iconfont icon-directions mr-1"></text>
							<text>导航</text>
						</view>
						<view class="action-button">
							<text class="iconfont icon-phone mr-1"></text>
							<text>电话</text>
						</view>
						<view class="action-button">
							<text class="iconfont icon-calendar-check mr-1"></text>
							<text>预订</text>
						</view>
					</view> -->
				</view>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMoreRestaurants && restaurants.length > 0" class="load-more" @click="loadMoreRestaurants">
				加载更多
			</view>
			<view v-else-if="restaurants.length > 0" class="no-more">
				没有更多了
			</view>

			<!-- 无数据提示 -->
			<view class="no-data" v-if="restaurants.length === 0">
				<image src="/static/images/no-data.png" class="no-data-image"></image>
				<text class="no-data-text">暂无数据</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentTime: '14:05',
			// 筛选相关
			currentFilterType: '', // 当前打开的筛选类型
			popupTitle: '', // 弹窗标题

			// 机构类型
			activeType: 10, // 当前选中的机构类型
			activeTypeText: '养老机构', // 显示的机构类型文本
			typeOptions: [
				// { name: '全部', value: 0 },
				{ name: '养老机构', value: 10 },
				{ name: '日间照料中心', value: 6 },
				{ name: '护理站', value: 7 },
				{ name: '长者照顾之家', value: 8 }
			],

			// 服务标签
			activeService: 0, // 当前选中的服务标签
			activeServiceText: '服务标签', // 显示的服务标签文本
			serviceOptions: [
				{ name: '全部', value: 0 },
				{ name: '医疗护理', value: 1 },
				{ name: '康复训练', value: 2 },
				{ name: '生活照料', value: 3 },
				{ name: '膳食服务', value: 4 },
				{ name: '文娱活动', value: 5 },
				{ name: '心理慰藉', value: 6 }
			],

			// 地区
			activeArea: 0, // 当前选中的地区
			activeAreaText: '宝山区', // 显示的地区文本
			areaOptions: [],

			// 临时存储筛选条件
			tempType: 10,
			tempService: 0,
			tempArea: 0,

			// 列表相关
			sfdsitetype: 10, // 机构类型参数
			sfdservice: 0, // 服务标签参数
			sfdarea: 0, // 地区参数
			restaurants: [],
			page: 1,
			pageSize: 5,
			hasMoreRestaurants: true,
			userLocation: {},
			hasLocation: false
		}
	},
	onLoad() {
		this.getUserLocation();
		// 加载数据
		this.loadMoreRestaurants();
		this.getOrgList();
	},
	methods: {
		getUserLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.userLocation = {
						latitude: res.latitude,
						longitude: res.longitude
					};
					this.hasLocation = true;
					// 如果已有数据，重新排序
					if (this.restaurants.length > 0) {
						this.sortRestaurantsByDistance();
					}
				},
				fail: (err) => {
					console.error('获取位置失败', err);
					uni.showToast({
						title: '获取位置失败，无法按距离排序',
						icon: 'none'
					});
				}
			});
		},
		sortRestaurantsByDistance() {
			if (!this.userLocation) return;
			
			this.restaurants.forEach(item => {
			if (item.latitude && item.longitude) {
				// 使用全局注册的地理位置工具
				item.distance = this.$geo.getDistance(
				this.userLocation,
				{ latitude: item.latitude, longitude: item.longitude }
				);
				item.distanceText = this.$geo.formatDistance(item.distance);
			} else {
				item.distance = Infinity;
				item.distanceText = '未知距离';
			}
			});
			
			// 按距离排序
			this.restaurants.sort((a, b) => a.distance - b.distance);
		},
		updateTime() {
			const now = new Date();
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}`;
		},
		goBack() {
			uni.navigateBack();
		},
		// 显示筛选弹窗
		showFilterPopup(type) {
			this.currentFilterType = type;

			// 设置弹窗标题和临时存储当前选中的值
			switch (type) {
				case 'type':
					this.popupTitle = '选择机构类型';
					this.tempType = this.activeType;
					break;
				case 'service':
					this.popupTitle = '选择服务标签';
					this.tempService = this.activeService;
					break;
				case 'area':
					this.popupTitle = '选择地区';
					this.tempArea = this.activeArea;
					break;
			}

			// 打开弹窗
			this.$refs.filterPopup.open();
		},

		// 关闭筛选弹窗
		closeFilterPopup() {
			// 恢复之前的选择
			switch (this.currentFilterType) {
				case 'type':
					this.tempType = this.activeType;
					break;
				case 'service':
					this.tempService = this.activeService;
					break;
				case 'area':
					this.tempArea = this.activeArea;
					break;
			}

			this.$refs.filterPopup.close();
		},

		// 选择筛选选项
		selectFilterOption(type, value, name) {
			switch (type) {
				case 'type':
					this.tempType = value;
					break;
				case 'service':
					this.tempService = value;
					break;
				case 'area':
					this.tempArea = value;
					break;
			}
		},

		// 重置筛选
		resetFilter() {
			switch (this.currentFilterType) {
				case 'type':
					this.tempType = 0; // 全部
					break;
				case 'service':
					this.tempService = 0; // 全部
					break;
				case 'area':
					this.tempArea = 0; // 全部
					break;
			}
		},

		// 确认筛选
		confirmFilter() {
			// 更新筛选条件
			switch (this.currentFilterType) {
				case 'type':
					this.activeType = this.tempType;
					this.sfdsitetype = this.activeType;

					// 更新显示文本
					const typeOption = this.typeOptions.find(item => item.value === this.activeType);
					this.activeTypeText = typeOption ? typeOption.name : '机构类型';
					break;
				case 'service':
					this.activeService = this.tempService;
					this.sfdservice = this.activeService;

					// 更新显示文本
					const serviceOption = this.serviceOptions.find(item => item.value === this.activeService);
					this.activeServiceText = serviceOption ? serviceOption.name : '服务标签';
					break;
				case 'area':
					this.activeArea = this.tempArea;
					this.sfdarea = this.activeArea;

					// 更新显示文本
					const areaOption = this.areaOptions.find(item => item.id === this.activeArea);
					this.activeAreaText = areaOption ? areaOption.name : '宝山区';
					break;
			}

			// 关闭弹窗
			this.$refs.filterPopup.close();

			// 重置分页并重新加载数据
			this.page = 1;
			this.restaurants = [];
			this.hasMoreRestaurants = true;
			this.loadMoreRestaurants();
		},
		loadMoreRestaurants() {
			if (this.hasMoreRestaurants) {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});

				// 构建请求参数
				let params = {
					current: this.page,
					size: this.pageSize,
					sfdsitetype: this.sfdsitetype // 机构类型
				};

				// 添加服务标签筛选条件
				if (this.sfdservice !== 0) {
					params.sfdservice = this.sfdservice;
				}

				// 添加地区筛选条件
				if (this.sfdarea !== 0) {
					params.sfdarea = this.sfdarea;
				}

				// 发送请求
				uni.request({
					url: this.$backUrl + '/biz/sysinformationdisplay/page-out',
					data: params,
					header: {},
					success: (res) => {
						console.log(res.data);
						if (res.data && res.data.data && res.data.data.records) {
							// 处理返回的餐厅数据
							const newRestaurants = res.data.data.records.map(item => {
								return {
									id: item.id, // 添加ID字段
									name: item.sfdname || '未命名餐厅',
									image: item.sfdimg || 'https://images.unsplash.com/photo-1555992336-fb0d29498b13?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
									rating: item.sfdrating || '4.5',
									price: item.sfdprice || '¥15起',
									distance: item.sfddistance || '1公里',
									latitude: item.sfdlatitude,
              						longitude: item.sfdlongitude,
									time: item.sfdtime || '营业中 08:00-20:00',
									tags: item.sfdtags ? item.sfdtags.split(',') : ['餐厅'],
									type: item.sfdtype || '社区食堂',
									phone: item.sfduphone || 'N/A'
								};
							});

							if (this.page === 1) {
								// 第一页，直接替换数据
								this.restaurants = newRestaurants;
							} else {
								// 不是第一页，追加数据
								this.restaurants = [...this.restaurants, ...newRestaurants];
							}

							// 如果有用户位置，按距离排序
							if (this.userLocation) {
								this.sortRestaurantsByDistance();
							}

							// 判断是否还有更多数据
							if (newRestaurants.length < this.pageSize) {
								this.hasMoreRestaurants = false;
							} else {
								// 页码加1，准备下次加载
								this.page++;
								this.hasMoreRestaurants = true;
							}
						} else {
							// 没有返回数据，设置没有更多
							this.hasMoreRestaurants = false;

							// 如果是第一页且没有数据，显示提示信息
							if (this.page === 1) {
								uni.showToast({
									title: '暂无餐厅数据',
									icon: 'none',
									duration: 2000
								});
							}
						}
					},
					fail: (err) => {
						console.error('获取餐厅列表失败', err);
						uni.showToast({
							title: '获取餐厅列表失败',
							icon: 'none',
							duration: 2000
						});

						// 设置没有更多数据
						this.hasMoreRestaurants = false;
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			}
		},
		viewInstitutionDetail: function(id) {
			if (!id) {
				uni.showToast({
					title: '机构ID不存在',
					icon: 'none'
				});
				return;
			}

			uni.navigateTo({
				url: `/pages/institution-detail/institution-detail?id=${id}&type=${this.sfdsitetype}`
			});
		},
		getOrgList() {
			uni.request({
				url: this.$backUrl + '/client/c/org/orgListSelector?parentId=1543842934270394368',
				header: {
					token: uni.getStorageSync('token')
				},
				success: (res) => {
					console.log(res.data);
					if (res.data && res.data.data && res.data.data.records) {
						this.areaOptions = [...res.data.data.records]
					}
				}
			})
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.ml-2 {
	margin-left: 8px;
}

.mr-1 {
	margin-right: 4px;
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

.text-gray-400 {
	color: #9ca3af;
}

/* 筛选条件 */
.filter-container {
	display: flex;
	background-color: white;
	padding: 10px 15px;
	margin-bottom: 20px;
}

.filter-item {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 8px 0;
	font-size: 14px;
	color: #333;
	position: relative;
}

.filter-item:not(:last-child)::after {
	content: '';
	position: absolute;
	right: 0;
	top: 25%;
	height: 50%;
	width: 1px;
	background-color: #eee;
}

.filter-text {
	margin-right: 5px;
}

/* 弹窗样式 */
.popup-content {
	background-color: white;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
	overflow: hidden;
	max-height: 70vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.popup-title {
	font-size: 16px;
	font-weight: bold;
}

.popup-close {
	font-size: 14px;
	color: #666;
}

.popup-body {
	padding: 0 15px;
	max-height: 50vh;
	overflow-y: auto;
}

.filter-option {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f0f0f0;
}

.filter-option:last-child {
	border-bottom: none;
}

.filter-option.active {
	color: #c8a287;
}

.popup-footer {
	display: flex;
	padding: 15px;
	border-top: 1px solid #f0f0f0;
}

.reset-btn {
	flex: 1;
	height: 40px;
	line-height: 40px;
	text-align: center;
	background-color: #f5f5f5;
	color: #333;
	border-radius: 20px;
	margin-right: 10px;
}

.confirm-btn {
	flex: 1;
	height: 40px;
	line-height: 40px;
	text-align: center;
	background-color: #c8a287;
	color: white;
	border-radius: 20px;
}

/* 用餐服务页面特定样式 */
.banner {
	height: 80px;
	background-size: cover;
	background-position: center;
	position: relative;
	margin-bottom: 20px;
	overflow: hidden;
}

.restaurant-list {
	padding: 0 15px;
}

.restaurant-item {
	background-color: white;
	border-radius: 10px;
	margin-bottom: 15px;
	overflow: hidden;
	box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.restaurant-image {
	height: 150px;
	background-size: cover;
	background-position: center;
	overflow: hidden;
}

.restaurant-info {
	padding: 15px;
}

.restaurant-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.restaurant-name {
	font-weight: bold;
	font-size: 16px;
}

.restaurant-rating {
	color: #ff9500;
}

.restaurant-details {
	display: flex;
	justify-content: space-between;
	color: #666;
	font-size: 14px;
	margin-bottom: 15px;
}

.restaurant-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 5px;
	margin-bottom: 15px;
}

.restaurant-tag {
	background-color: #f5f5f5;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
}

.restaurant-actions {
	display: flex;
	justify-content: space-between;
	border-top: 1px solid #eee;
	padding-top: 15px;
}

.action-button {
	display: flex;
	align-items: center;
	color: #c8a287;
	font-size: 14px;
}

/* 临时图标样式，实际应使用iconfont */
.iconfont {
	font-family: "iconfont";
}

.icon-signal:before {
	content: "\e8d7";
}

.icon-wifi:before {
	content: "\e8d8";
}

.icon-battery-full:before {
	content: "\e8d9";
}

.icon-arrow-left:before {
	content: "\e8f4";
}

.icon-ellipsis-h:before {
	content: "\e8f5";
}

.icon-star:before {
	content: "\e8f6";
}

.icon-directions:before {
	content: "\e8f1";
}

.icon-phone:before {
	content: "\e8f2";
}

.icon-calendar-check:before {
	content: "\e8f7";
}

/* 加载更多和无数据提示样式 */
.load-more, .no-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #999;
}

.load-more {
	color: #c8a287;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}
</style>
