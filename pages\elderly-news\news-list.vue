<template>
	<view class="container">
		<!-- 资讯列表页面头部 -->
		<view class="header">
			<view class="title">养老资讯</view>
			<view class="subtitle">为老年人提供实用的养老资讯</view>
		</view>

		<!-- 资讯分类标签 -->
		<view class="category-tabs">
			<view
				v-for="(tab, index) in categories"
				:key="index"
				:class="['category-tab', currentTab === index ? 'active-tab' : '']"
				@click="changeTab(index)"
			>
				{{tab.name}}
			</view>
		</view>

		<!-- 资讯列表 -->
		<view class="news-list">
			<view
				class="news-item"
				v-for="(news, index) in filteredNews"
				:key="index"
				@click="goToDetail(news.id)"
			>
				<image :src="news.coverImage || defaultImage" mode="aspectFill" class="news-image"></image>
				<view class="news-content">
					<view class="news-title">{{news.title}}</view>
					<view class="news-meta">
						<view class="news-time">
							<my-icon type="time" size="16" color="#BE957E" class="icon-mr"></my-icon>
							<text>{{news.publishDate}}</text>
						</view>
						<view class="news-category">
							<my-icon type="tag" size="16" color="#BE957E" class="icon-mr"></my-icon>
							<text>{{getCategoryName(news.category)}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 无数据提示 -->
			<view class="no-data" v-if="filteredNews.length === 0">
				<image src="/static/images/no-data.png" class="no-data-image"></image>
				<text class="no-data-text">暂无相关资讯</text>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMoreNews && filteredNews.length > 0" class="load-more" @click="loadMoreNews">
				加载更多
			</view>
			<view v-else-if="filteredNews.length > 0" class="no-more">
				没有更多资讯了
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			defaultImage: 'https://images.unsplash.com/photo-1493676304819-0d7a8d026dcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
			currentTab: 0,
			categories: [
				{ name: '全部', id: 'all' },
				{ name: '医养', id: 'medical' },
				{ name: '康养', id: 'health' },
				{ name: '悦养', id: 'joy' }
			],
			newsList: [],
			page: 1,
			pageSize: 10,
			hasMoreNews: true
		}
	},
	computed: {
		filteredNews() {
			if (this.currentTab === 0) {
				return this.newsList;
			} else {
				return this.newsList.filter(item => item.category === this.categories[this.currentTab].id);
			}
		}
	},
	onLoad() {
		this.loadMoreNews();
	},
	methods: {
		loadMoreNews() {
			if (this.hasMoreNews) {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});

				// 模拟从服务器获取数据
				setTimeout(() => {
					// 模拟数据
					const mockData = this.generateMockData();
					
					if (this.page === 1) {
						// 第一页，直接替换数据
						this.newsList = mockData;
					} else {
						// 不是第一页，追加数据
						this.newsList = [...this.newsList, ...mockData];
					}

					// 判断是否还有更多数据
					if (mockData.length < this.pageSize) {
						this.hasMoreNews = false;
					} else {
						// 页码加1，准备下次加载
						this.page++;
						this.hasMoreNews = true;
					}

					uni.hideLoading();
				}, 1000);
			}
		},
		generateMockData() {
			// 模拟数据生成
			const result = [];
			const categoryIds = ['medical', 'health', 'joy'];
			const titles = [
				'老年人健康饮食指南：这些食物助您延年益寿',
				'居家养老新模式：智能设备让生活更便捷',
				'老年人心理健康：如何保持积极心态',
				'养老保险政策解读：这些福利您知道吗',
				'老年人运动指南：适合银发族的健身方式',
				'防骗指南：老年人如何防范电信诈骗',
				'老年人用药安全：这些注意事项不可忽视',
				'老年人社交活动：丰富晚年生活的方式',
				'老年人营养补充：维生素与矿物质的重要性',
				'老年人居家安全：防跌倒措施全指南'
			];
			
			// 根据当前页码生成不同的数据
			const startIndex = (this.page - 1) * this.pageSize % titles.length;
			
			for (let i = 0; i < this.pageSize && i < 10; i++) {
				const index = (startIndex + i) % titles.length;
				const categoryIndex = (index % 3);
				
				result.push({
					id: `news-${this.page}-${i}`,
					title: titles[index],
					coverImage: `https://picsum.photos/id/${70 + index}/200/200`,
					publishDate: `2023-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 28) + 1}`,
					category: categoryIds[categoryIndex],
					content: '这是资讯的详细内容，包含了丰富的信息和指导。'
				});
			}
			
			return result;
		},
		changeTab(index) {
			if (this.currentTab !== index) {
				this.currentTab = index;
			}
		},
		goToDetail(id) {
			uni.navigateTo({
				url: `/pages/elderly-news/news-detail?id=${id}`
			});
		},
		getCategoryName(categoryId) {
			const category = this.categories.find(item => item.id === categoryId);
			return category ? category.name : '其他';
		}
	}
}
</script>

<style>
.container {
	padding: 20px;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.header {
	margin-bottom: 20px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.subtitle {
	font-size: 14px;
	color: #999;
	margin-top: 5px;
}

.category-tabs {
	display: flex;
	flex-wrap: nowrap;
	overflow-x: scroll;
	margin-bottom: 20px;
	background-color: #fff;
	padding: 10px 0;
	border-radius: 8px;
}

.category-tab {
	padding: 6px 16px;
	margin: 0 5px;
	font-size: 14px;
	border-radius: 20px;
	color: #666;
	white-space: nowrap;
}

.active-tab {
	background-color: #BE957E;
	color: #FDF3EA;
}

.news-list {
	padding-bottom: 30px;
}

.news-item {
	background-color: #fff;
	border-radius: 12px;
	margin-bottom: 15px;
	overflow: hidden;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	display: flex;
	padding: 15px;
}

.news-image {
	width: 120px;
	height: 90px;
	border-radius: 8px;
	flex-shrink: 0;
}

.news-content {
	flex: 1;
	padding-left: 15px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.news-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 10px;
	/* 文本超出两行显示省略号 */
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.news-meta {
	font-size: 12px;
	color: #999;
	display: flex;
	justify-content: space-between;
}

.news-time, .news-category {
	display: flex;
	align-items: center;
}

.icon-mr {
	margin-right: 5px;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}

.load-more, .no-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #999;
}

.load-more {
	color: #BE957E;
}
</style>
