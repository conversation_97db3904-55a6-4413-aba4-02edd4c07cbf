<template>
	<view class="container">
		<!-- 导航栏 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<uni-icons type="arrow-left" size="24" color="#333"></uni-icons>
			</view>
			<view class="page-title">{{ isAddMode ? '添加紧急联系人' : '编辑紧急联系人' }}</view>
			<view style="width: 24px;"></view>
		</view> -->

		<!-- 表单 -->
		<view class="form-container">
			<!-- 姓名 -->
			<view class="form-item">
				<view class="form-label">姓名</view>
				<input
					class="form-input"
					type="text"
					v-model="contactForm.name"
					placeholder="请输入联系人姓名"
					maxlength="20"
				/>
			</view>

			<!-- 关系 -->
			<view class="form-item">
				<view class="form-label">关系</view>
				<picker
					class="form-picker"
					:range="relationOptions"
					range-key="label"
					:value="relationIndex"
					@change="onRelationChange"
				>
					<view class="picker-value">
						{{ contactForm.relation || '请选择与联系人的关系' }}
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</picker>
			</view>

			<!-- 手机号 -->
			<view class="form-item">
				<view class="form-label">手机号</view>
				<input
					class="form-input"
					type="number"
					v-model="contactForm.phone"
					placeholder="请输入联系人手机号"
					maxlength="11"
				/>
			</view>

			<!-- 备注 -->
			<view class="form-item">
				<view class="form-label">备注</view>
				<textarea
					class="form-textarea"
					v-model="contactForm.remark"
					placeholder="请输入备注信息（选填）"
					maxlength="100"
				/>
				<view class="input-counter">{{ contactForm.remark.length }}/100</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-button" @click="saveContact">
			保存
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isAddMode: true,
			contactId: null,
			contactForm: {
				id: null,
				name: '',
				relation: '',
				phone: '',
				remark: ''
			},
			relationOptions: [
				{ label: '父亲', value: '父亲' },
				{ label: '母亲', value: '母亲' },
				{ label: '子女', value: '子女' },
				{ label: '配偶', value: '配偶' },
				{ label: '兄弟姐妹', value: '兄弟姐妹' },
				{ label: '朋友', value: '朋友' },
				{ label: '医生', value: '医生' },
				{ label: '护工', value: '护工' },
				{ label: '其他', value: '其他' }
			],
			relationIndex: 0
		}
	},
	onLoad(options) {
		// 判断是添加模式还是编辑模式
		if (options.mode === 'add') {
			this.isAddMode = true;
			// 生成一个新的ID
			//this.contactForm.id = Date.now();
		} else if (options.mode === 'edit' && options.id) {
			this.isAddMode = false;
			this.contactId = options.id;
			this.getContactInfo();
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		// 获取联系人信息
		getContactInfo() {
			const contactsListData = uni.getStorageSync('emergencyContacts');
			if (contactsListData) {
				const contactsList = JSON.parse(contactsListData);
				const contact = contactsList.find(item => item.id === this.contactId);
				if (contact) {
					this.contactForm = { ...contact };
					// 设置关系选择器的索引
					this.setRelationIndex();
				}
			}
		},
		// 设置关系选择器的索引
		setRelationIndex() {
			const index = this.relationOptions.findIndex(item => item.value === this.contactForm.relation);
			this.relationIndex = index !== -1 ? index : 0;
		},
		// 关系选择变更
		onRelationChange(e) {
			const index = e.detail.value;
			this.relationIndex = index;
			this.contactForm.relation = this.relationOptions[index].value;
		},
		// 保存联系人
		saveContact() {
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			// 获取现有联系人列表
			let contactsList = [];
			const contactsListData = uni.getStorageSync('emergencyContacts');
			if (contactsListData) {
				try {
					const parsedData = JSON.parse(contactsListData);
					// 确保 contactsList 是数组
					contactsList = Array.isArray(parsedData) ? parsedData : [];
				} catch (e) {
					console.error('解析联系人列表失败', e);
					// 如果解析失败，使用空数组
					contactsList = [];
				}
			}

			let reqUrl = '';

			if (this.isAddMode) {
				// 添加模式：添加到列表
				contactsList.push(this.contactForm);
				reqUrl = '/client/c/emergencycontacts/add';
			} else {
				reqUrl = '/client/c/emergencycontacts/edit';
				// 编辑模式：更新列表中的对应项
				const index = contactsList.findIndex(item => item.id === this.contactForm.id);
				if (index !== -1) {
					contactsList[index] = this.contactForm;
				} else {
					// 如果找不到对应项，添加到列表
					contactsList.push(this.contactForm);
				}
			}

			// 保存到本地存储
			uni.setStorageSync('emergencyContacts', JSON.stringify(contactsList));

			uni.request({
				url: this.$backUrl + reqUrl,
				method: 'POST',
				header: {
					'token': uni.getStorageSync('token')
				},
				data: this.contactForm,
				success: () => {
					uni.showToast({
						title: '保存成功',
						icon: 'success',
						success: () => {
							// 延迟返回上一页
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					});
				},
				fail: (err) => {
					console.error('紧急联系人更新失败', err);
				}
			})
		},
		// 表单验证
		validateForm() {
			if (!this.contactForm.name.trim()) {
				uni.showToast({
					title: '请输入联系人姓名',
					icon: 'none'
				});
				return false;
			}

			if (!this.contactForm.relation) {
				uni.showToast({
					title: '请选择与联系人的关系',
					icon: 'none'
				});
				return false;
			}

			if (!this.contactForm.phone) {
				uni.showToast({
					title: '请输入联系人手机号',
					icon: 'none'
				});
				return false;
			}

			// 验证手机号格式
			const phoneRegex = /^1[3-9]\d{9}$/;
			if (!phoneRegex.test(this.contactForm.phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return false;
			}

			return true;
		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 80px; /* 为底部按钮留出空间 */
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

.form-container {
	margin-top: 10px;
	padding: 0 15px;
}

.form-item {
	background-color: white;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 10px;
}

.form-label {
	font-size: 14px;
	color: #666;
	margin-bottom: 10px;
}

.form-input {
	width: 100%;
	height: 40px;
	font-size: 16px;
	color: #333;
}

.form-picker {
	width: 100%;
}

.picker-value {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 40px;
	font-size: 16px;
	color: #333;
}

.form-textarea {
	width: 100%;
	height: 100px;
	font-size: 16px;
	color: #333;
}

.input-counter {
	text-align: right;
	font-size: 12px;
	color: #999;
	margin-top: 5px;
}

.save-button {
	position: fixed;
	bottom: 20px;
	left: 20px;
	right: 20px;
	height: 50px;
	line-height: 50px;
	text-align: center;
	background-color: #c8a287;
	color: white;
	border-radius: 25px;
	font-size: 16px;
	font-weight: bold;
	box-shadow: 0 2px 10px rgba(200, 162, 135, 0.5);
}
</style>
