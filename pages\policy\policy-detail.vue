<template>
	<view class="container" :class="{ 'with-audio-progress': isPlaying || isPaused }">
		<!-- 应用头部 -->
		<!-- <view class="app-header">
			<view class="flex items-center" @click="goBack">
				<my-icon type="arrow-left" size="24" color="#333"></my-icon>
				<text class="ml-2">返回</text>
			</view>
			<view class="text-center flex-1 font-bold">政策详情</view>
			<view class="w-8"></view>
		</view> -->

		<!-- 政策详情 -->
		<view class="policy-detail" v-if="policy">
			<view class="policy-title">{{policy.spname}}</view>

			<view class="policy-meta">
				<view class="meta-item">
					<my-icon type="calendar" size="14" color="#999"></my-icon>
					<text class="ml-1">{{policy.adddate}}</text>
				</view>
				<!-- <view class="meta-item">
					<my-icon type="info" size="14" color="#999"></my-icon>
					<text class="ml-1">{{policy.source}}</text>
				</view> -->
			</view>

			<!-- <view class="policy-tags">
				<view class="policy-tag" v-for="(tag, index) in policy.tags" :key="index">
					{{tag}}
				</view>
			</view> -->

			<view class="policy-content">
				<rich-text :nodes="policy.spconent"></rich-text>
			</view>

			<!-- 相关政策 -->
			<!-- <view class="related-policies" v-if="relatedPolicies.length > 0">
				<view class="section-title">相关政策</view>
				<view
					v-for="(item, index) in relatedPolicies"
					:key="index"
					class="related-policy-item"
					@click="viewPolicyDetail(item.id)"
				>
					<view class="related-policy-title">{{item.title}}</view>
					<my-icon type="arrow-right" size="16" color="#C8A287"></my-icon>
				</view>
			</view> -->

			<!-- 政策解读 -->
			<!-- <view class="policy-interpretation" v-if="policyInterpretation">
				<view class="section-title">政策解读</view>
				<view class="interpretation-content">
					<rich-text :nodes="policyInterpretation"></rich-text>
				</view>
			</view> -->

			<!-- 咨询方式 -->
			<!-- <view class="consultation-methods">
				<view class="section-title">咨询方式</view>
				<view class="consultation-item" @click="makePhoneCall('021-12345')">
					<view class="consultation-icon">
						<my-icon type="phone" size="20" color="#C8A287"></my-icon>
					</view>
					<view class="consultation-info">
						<view class="consultation-title">电话咨询</view>
						<view class="consultation-detail">021-12345</view>
					</view>
					<my-icon type="phone" size="20" color="#C8A287"></my-icon>
				</view>
				<view class="consultation-item">
					<view class="consultation-icon">
						<my-icon type="location" size="20" color="#C8A287"></my-icon>
					</view>
					<view class="consultation-info">
						<view class="consultation-title">现场咨询</view>
						<view class="consultation-detail">宝山区政务服务中心（宝山区友谊路18号）</view>
					</view>
				</view>
			</view> -->
		</view>
		<view v-else>
			<h2>未查询到详情</h2>
		</view>

		<!-- 音频播放进度条 -->
		<view class="audio-progress-container" v-if="isPlaying || isPaused">
			<view class="progress-info">
				<text class="progress-time">{{ formatTime(currentTime) }}</text>
				<text class="progress-title">{{ policy.spname }}</text>
				<text class="progress-time">{{ formatTime(duration) }}</text>
			</view>
			<view class="progress-bar-container">
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn share-btn" open-type="share">
				<my-icon type="share" size="20" color="#333"></my-icon>
				<text>分享</text>
			</button>
			<!-- <button class="action-btn save-btn" @click="savePolicy">
				<my-icon type="star" size="20" color="#333"></my-icon>
				<text>收藏</text>
			</button> -->
			<button class="action-btn print-btn" @click="toggleAudioPlay">
				<my-icon :type="audioButtonIcon" size="20" color="#333"></my-icon>
				<text>{{ audioButtonText }}</text>
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			policyId: null,
			policy: null,
			relatedPolicies: [],
			policyInterpretation: '',
			isSaved: false,
			// 音频播放相关
			innerAudioContext: null,
			isPlaying: false,
			isPaused: false,
			currentTime: 0,
			duration: 0,
			progressTimer: null
		}
	},
	computed: {
		// 音频按钮图标
		audioButtonIcon() {
			if (this.isPlaying) {
				return 'pause';
			} else if (this.isPaused) {
				return 'play';
			} else {
				return 'speak';
			}
		},
		// 音频按钮文本
		audioButtonText() {
			if (this.isPlaying) {
				return '暂停';
			} else if (this.isPaused) {
				return '继续';
			} else {
				return '朗读';
			}
		},
		// 播放进度百分比
		progressPercent() {
			if (this.duration === 0) return 0;
			return (this.currentTime / this.duration) * 100;
		}
	},
	onLoad(options) {
		if (options.id) {
			this.policyId = parseInt(options.id);
			this.loadPolicyDetail();
		}
	},
	// 页面卸载时重置播放状态
	onUnload() {
		this.resetAudioState();
	},
	// 页面隐藏时暂停播放
	onHide() {
		if (this.isPlaying) {
			this.pauseAudio();
		}
	},
	// 添加分享消息处理函数
	onShareAppMessage() {
		let title = '养老政策详情';
		let path = '/pages/policy/policy-detail?id=' + this.policyId;

		// 如果政策信息已加载，则使用政策标题
		if (this.policy) {
			title = this.policy.spname;
		}

		return {
			title: title,
			path: path,
			imageUrl: '/static/share-policy.png' // 可以添加默认分享图片
		};
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		loadPolicyDetail() {
			uni.request({
			    url: this.$backUrl + '/biz/syspolic/detail-out?id=' + this.policyId,
			    data: {},
			    header: {},
			    success: (res) => {
					this.policy = res.data.data;
			    }
			})
		},
		viewPolicyDetail(id) {
			// 如果是当前政策，不做跳转
			if (id === this.policyId) return;

			// 跳转到新的政策详情页
			uni.navigateTo({
				url: `/pages/policy/policy-detail?id=${id}`
			});
		},
		makePhoneCall(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber: phoneNumber,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
				}
			});
		},
		sharePolicy() {
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		},
		savePolicy() {
			this.isSaved = !this.isSaved;
			uni.showToast({
				title: this.isSaved ? '已收藏' : '已取消收藏',
				icon: 'success'
			});
		},
		// 切换音频播放状态
		toggleAudioPlay() {
			if (!this.policy || !this.policy.voiceUrl) {
				uni.showToast({
					title: '暂无播报语音',
					icon: 'none'
				});
				return;
			}

			if (this.isPlaying) {
				this.pauseAudio();
			} else if (this.isPaused) {
				this.resumeAudio();
			} else {
				this.startAudio();
			}
		},

		// 开始播放音频
		startAudio() {
			if (this.innerAudioContext) {
				this.innerAudioContext.destroy();
			}

			this.innerAudioContext = uni.createInnerAudioContext();
			this.innerAudioContext.src = this.policy.voiceUrl;
			this.innerAudioContext.autoplay = true;

			// 监听播放事件
			this.innerAudioContext.onPlay(() => {
				console.log('开始播放');
				this.isPlaying = true;
				this.isPaused = false;
				this.startProgressTimer();
			});

			// 监听暂停事件
			this.innerAudioContext.onPause(() => {
				console.log('暂停播放');
				this.isPlaying = false;
				this.isPaused = true;
				this.stopProgressTimer();
			});

			// 监听播放结束事件
			this.innerAudioContext.onEnded(() => {
				console.log('播放结束');
				this.resetAudioState();
			});

			// 监听错误事件
			this.innerAudioContext.onError((res) => {
				console.log('播放错误:', res.errMsg, res.errCode);
				this.resetAudioState();
				uni.showToast({
					title: '播放失败',
					icon: 'none'
				});
			});

			// 监听音频加载完成
			this.innerAudioContext.onCanplay(() => {
				this.duration = this.innerAudioContext.duration || 0;
			});
		},

		// 暂停音频
		pauseAudio() {
			if (this.innerAudioContext && this.isPlaying) {
				this.innerAudioContext.pause();
			}
		},

		// 继续播放音频
		resumeAudio() {
			if (this.innerAudioContext && this.isPaused) {
				this.innerAudioContext.play();
			}
		},

		// 重置音频状态
		resetAudioState() {
			this.isPlaying = false;
			this.isPaused = false;
			this.currentTime = 0;
			this.duration = 0;
			this.stopProgressTimer();

			if (this.innerAudioContext) {
				this.innerAudioContext.destroy();
				this.innerAudioContext = null;
			}
		},

		// 开始进度计时器
		startProgressTimer() {
			this.stopProgressTimer();
			this.progressTimer = setInterval(() => {
				if (this.innerAudioContext && this.isPlaying) {
					this.currentTime = this.innerAudioContext.currentTime || 0;
					this.duration = this.innerAudioContext.duration || 0;
				}
			}, 1000);
		},

		// 停止进度计时器
		stopProgressTimer() {
			if (this.progressTimer) {
				clearInterval(this.progressTimer);
				this.progressTimer = null;
			}
		},

		// 格式化时间显示
		formatTime(seconds) {
			if (!seconds || isNaN(seconds)) return '00:00';
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = Math.floor(seconds % 60);
			return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 60px; /* 为底部操作栏留出空间 */
}

/* 当有音频播放时，增加额外的底部间距 */
.container.with-audio-progress {
	padding-bottom: 120px; /* 为底部操作栏和进度条留出空间 */
}

.app-header {
	display: flex;
	align-items: center;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.policy-detail {
	padding: 15px;
	background-color: white;
	margin-bottom: 15px;
}

.policy-title {
	font-size: 18px;
	font-weight: bold;
	line-height: 1.4;
	margin-bottom: 15px;
}

.policy-meta {
	display: flex;
	margin-bottom: 15px;
}

.meta-item {
	display: flex;
	align-items: center;
	font-size: 12px;
	color: #999;
	margin-right: 15px;
}

.policy-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20px;
}

.policy-tag {
	background-color: #f5f5f5;
	color: #666;
	font-size: 12px;
	padding: 4px 8px;
	border-radius: 4px;
	margin-right: 8px;
	margin-bottom: 8px;
}

.policy-content {
	font-size: 14px;
	line-height: 1.6;
	color: #333;
	margin-bottom: 30px;
}

.policy-content h3 {
	font-size: 16px;
	font-weight: bold;
	margin: 20px 0 10px;
	color: #333;
}

.policy-content p {
	margin-bottom: 10px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
	padding-left: 10px;
	border-left: 3px solid #C8A287;
}

.related-policies {
	background-color: white;
	padding: 15px;
	margin-bottom: 15px;
}

.related-policy-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.related-policy-item:last-child {
	border-bottom: none;
}

.related-policy-title {
	font-size: 14px;
	color: #333;
	flex: 1;
	margin-right: 10px;
}

.policy-interpretation {
	background-color: white;
	padding: 15px;
	margin-bottom: 15px;
}

.interpretation-content {
	font-size: 14px;
	line-height: 1.6;
	color: #333;
}

.consultation-methods {
	background-color: white;
	padding: 15px;
	margin-bottom: 15px;
}

.consultation-item {
	display: flex;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.consultation-item:last-child {
	border-bottom: none;
}

.consultation-icon {
	margin-right: 15px;
}

.consultation-info {
	flex: 1;
}

.consultation-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
}

.consultation-detail {
	font-size: 12px;
	color: #666;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background-color: white;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
	z-index: 998; /* 确保底部操作栏在进度条下方 */
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: transparent;
	border: none;
	font-size: 12px;
	color: #333;
	padding: 5px 0;
}

.action-btn::after {
	border: none;
}

.action-btn text {
	margin-top: 5px;
}

/* 音频播放进度条样式 */
.audio-progress-container {
	position: fixed;
	bottom: 115px;
	left: 0;
	right: 0;
	background-color: white;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
	border-top: 1px solid #f0f0f0;
	z-index: 999; /* 确保进度条在底部操作栏上方 */
}

.progress-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.progress-time {
	font-size: 12px;
	color: #666;
	min-width: 40px;
}

.progress-title {
	flex: 1;
	font-size: 14px;
	color: #333;
	text-align: center;
	margin: 0 10px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.progress-bar-container {
	width: 100%;
}

.progress-bar {
	width: 100%;
	height: 4px;
	background-color: #f0f0f0;
	border-radius: 2px;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background-color: #c8a287;
	border-radius: 2px;
	transition: width 0.3s ease;
}
</style>
