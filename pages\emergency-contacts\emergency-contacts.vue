<template>
	<view class="container">
		<!-- 导航栏 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<uni-icons type="arrow-left" size="24" color="#333"></uni-icons>
			</view>
			<view class="page-title">紧急联系人</view>
			<view style="width: 24px;"></view>
		</view> -->

		<!-- 紧急联系人列表 -->
		<view class="contacts-list">
			<view v-if="contactsList.length > 0">
				<view
					class="contact-item"
					v-for="(item, index) in contactsList"
					:key="index"
					@click="editContact(item)"
				>
					<view class="contact-info">
						<view class="contact-name">{{ item.name }}</view>
						<view class="contact-relation">{{ item.relation }}</view>
						<view class="contact-phone">{{ formatPhone(item.phone) }}</view>
					</view>
					<view class="contact-actions">
						<view class="action-btn call-btn" @click.stop="callContact(item)">
							<uni-icons type="phone" size="20" color="#fff"></uni-icons>
						</view>
						<view class="action-btn delete-btn" @click.stop="confirmDeleteContact(item)">
							<uni-icons type="trash" size="20" color="#fff"></uni-icons>
						</view>
					</view>
				</view>
			</view>

			<!-- 无数据提示 -->
			<view class="no-data" v-else>
				<image src="/static/images/no-data.png" class="no-data-image"></image>
				<text class="no-data-text">暂无紧急联系人</text>
				<text class="no-data-tips">请添加紧急联系人，以便在紧急情况下联系</text>
			</view>
		</view>

		<!-- 添加按钮 -->
		<view class="add-button" @click="addContact">
			<uni-icons type="plusempty" size="24" color="#fff"></uni-icons>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			contactsList: [],
			page: 1,
			pageSize: 10,
		}
	},
	onLoad() {
		//this.getContactsList();
	},
	onShow() {
		// 每次显示页面时刷新列表
		this.getContactsList();
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		// 获取紧急联系人列表
		getContactsList() {
			// 从本地存储获取紧急联系人列表
			//const contactsListData = uni.getStorageSync('emergencyContacts');
			/* if (contactsListData || !contactsListData.includes('[]') === 0) {
				try {
					const parsedData = JSON.parse(contactsListData);
					// 确保 contactsList 是数组
					this.contactsList = Array.isArray(parsedData) ? parsedData : [];
				} catch (e) {
					console.error('解析联系人列表失败', e);
					// 如果解析失败，使用空列表
					this.contactsList = [];
					// 重新初始化存储
					uni.setStorageSync('emergencyContacts', JSON.stringify([]));
				}
			} else { */
				// 初始化空列表
				this.contactsList = [];
				uni.request({
					url: this.$backUrl + '/client/c/emergencycontacts/page',
					method: 'GET',
					data: {
						current: this.page,  // 当前页码
						size: this.pageSize  // 每页数据量
					},
					header: {
						'token': uni.getStorageSync('token')
					},
					success: (res) => {
						if (res.data && res.data.data && Array.isArray(res.data.data.records)) {
							this.contactsList = res.data.data.records;
						} else {
							this.contactsList = [];
						}
						// 保存到本地存储
						uni.setStorageSync('emergencyContacts', JSON.stringify(this.contactsList));
					},
					fail: () => {
						// 请求失败时初始化为空数组
						this.contactsList = [];
						uni.setStorageSync('emergencyContacts', JSON.stringify([]));
					}
				});
		},
		// 格式化手机号
		formatPhone(phone) {
			if (!phone) return '';
			// 格式化手机号为 136****5678 的形式
			if (phone.length === 11) {
				return phone.substring(0, 3) + '****' + phone.substring(7);
			}
			return phone;
		},
		// 添加紧急联系人
		addContact() {
			uni.navigateTo({
				url: '/pages/emergency-contacts/contact-edit?mode=add'
			});
		},
		// 编辑紧急联系人
		editContact(contact) {
			uni.navigateTo({
				url: `/pages/emergency-contacts/contact-edit?mode=edit&id=${contact.id}`
			});
		},
		// 拨打紧急联系人电话
		callContact(contact) {
			uni.makePhoneCall({
				phoneNumber: contact.phone,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.error('拨打电话失败', err);
					uni.showToast({
						title: '拨打电话失败',
						icon: 'none'
					});
				}
			});
		},
		// 确认删除紧急联系人
		confirmDeleteContact(contact) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除紧急联系人"${contact.name}"吗？`,
				success: (res) => {
					if (res.confirm) {
						this.deleteContact(contact);
					}
				}
			});
		},
		// 删除紧急联系人
		deleteContact(contact) {
			uni.request({
				url: this.$backUrl + '/client/c/emergencycontacts/delete',
				method: 'POST',
				header: {
					'token': uni.getStorageSync('token')
				},
				data: [
					{
					id: contact.id
				}
				],
				success: (res) => {
					if (res.data.code === 200) {
						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
						// 从列表中移除
						this.contactsList = this.contactsList.filter(item => item.id !== contact.id);
						// 保存到本地存储
						uni.setStorageSync('emergencyContacts', JSON.stringify(this.contactsList));
					}
				}
			})
		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 80px; /* 为底部按钮留出空间 */
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

.contacts-list {
	margin-top: 10px;
	padding: 0 15px;
}

.contact-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	background-color: white;
	margin-bottom: 10px;
	border-radius: 8px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.contact-info {
	flex: 1;
}

.contact-name {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 5px;
}

.contact-relation {
	font-size: 14px;
	color: #c8a287;
	background-color: rgba(200, 162, 135, 0.1);
	padding: 2px 8px;
	border-radius: 10px;
	display: inline-block;
	margin-bottom: 5px;
}

.contact-phone {
	font-size: 14px;
	color: #666;
}

.contact-actions {
	display: flex;
}

.action-btn {
	width: 36px;
	height: 36px;
	border-radius: 18px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 10px;
}

.call-btn {
	background-color: #4caf50;
}

.delete-btn {
	background-color: #f44336;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 16px;
	color: #333;
	margin-bottom: 10px;
}

.no-data-tips {
	font-size: 14px;
	color: #999;
	text-align: center;
}

.add-button {
	position: fixed;
	bottom: 30px;
	right: 30px;
	width: 60px;
	height: 60px;
	background-color: #c8a287;
	border-radius: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 10px rgba(200, 162, 135, 0.5);
}
</style>
