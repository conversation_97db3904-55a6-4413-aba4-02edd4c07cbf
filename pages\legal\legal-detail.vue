<template>
	<view class="container">
		<!-- 法律法规详情 -->
		<view class="legal-detail" v-if="legal">
			<view class="legal-title">{{legal.title}}</view>
			
			<view class="legal-meta">
				<view class="meta-item">
					<my-icon type="calendar" size="14" color="#999"></my-icon>
					<text class="ml-1">{{legal.publishDate}}</text>
				</view>
				<view class="meta-item">
					<my-icon type="info" size="14" color="#999"></my-icon>
					<text class="ml-1">{{legal.source}}</text>
				</view>
			</view>
			
			<view class="legal-tags">
				<view class="legal-tag" v-for="(tag, index) in legal.tags" :key="index">
					{{tag}}
				</view>
			</view>
			
			<view class="legal-content">
				<rich-text :nodes="formattedContent"></rich-text>
			</view>
			
			<!-- 相关法律法规 -->
			<view class="related-legals" v-if="relatedLegals.length > 0">
				<view class="section-title">相关法律法规</view>
				<view 
					v-for="(item, index) in relatedLegals" 
					:key="index" 
					class="related-legal-item"
					@click="viewLegalDetail(item.id)"
				>
					<view class="related-legal-title">{{item.title}}</view>
					<my-icon type="arrow-right" size="16" color="#C8A287"></my-icon>
				</view>
			</view>
			
			<!-- 法律解读 -->
			<view class="legal-interpretation" v-if="legalInterpretation">
				<view class="section-title">法律解读</view>
				<view class="interpretation-content">
					<rich-text :nodes="legalInterpretation"></rich-text>
				</view>
			</view>
			
			<!-- 咨询方式 -->
			<view class="consultation-methods">
				<view class="section-title">咨询方式</view>
				<view class="consultation-item" @click="makePhoneCall('021-12345')">
					<view class="consultation-icon">
						<my-icon type="phone" size="20" color="#C8A287"></my-icon>
					</view>
					<view class="consultation-info">
						<view class="consultation-title">电话咨询</view>
						<view class="consultation-detail">021-12345</view>
					</view>
					<my-icon type="phone" size="20" color="#C8A287"></my-icon>
				</view>
				<view class="consultation-item">
					<view class="consultation-icon">
						<my-icon type="location" size="20" color="#C8A287"></my-icon>
					</view>
					<view class="consultation-info">
						<view class="consultation-title">现场咨询</view>
						<view class="consultation-detail">宝山区政务服务中心（宝山区友谊路18号）</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 无数据提示 -->
		<view class="no-data" v-else>
			<image src="/static/images/no-data.png" class="no-data-image"></image>
			<text class="no-data-text">未查询到详情</text>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn share-btn" open-type="share">
				<my-icon type="share" size="20" color="#333"></my-icon>
				<text>分享</text>
			</button>
			<!-- <button class="action-btn save-btn" @click="saveLegal">
				<my-icon type="star" size="20" color="#333" :class="{'active': isSaved}"></my-icon>
				<text>{{isSaved ? '已收藏' : '收藏'}}</text>
			</button> -->
			<button class="action-btn print-btn" @click="voiceSpeak(legal.voiceUrl)">
				<my-icon type="speak" size="20" color="#333"></my-icon>
				<text>朗读</text>
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			legalId: null,
			legal: null,
			relatedLegals: [],
			legalInterpretation: '',
			isSaved: false
		}
	},
	computed: {
		formattedContent() {
			// 格式化法律法规内容
			if (!this.legal || !this.legal.content) return '';
			
			// 将换行符转换为HTML标签
			let content = this.legal.content;
			content = content.replace(/\n\n/g, '</p><p>');
			content = content.replace(/\n/g, '<br>');
			content = `<p>${content}</p>`;
			
			return content;
		}
	},
	onLoad(options) {
		if (options.id) {
			this.legalId = parseInt(options.id);
			this.loadLegalDetail();
			this.loadRelatedLegals();
			this.loadLegalInterpretation();
		}
	},
	onShareAppMessage() {
		if (this.legal) {
			return {
				title: this.legal.title,
				path: `/pages/legal/legal-detail?id=${this.legalId}`,
				imageUrl: '/static/images/share-legal.png', // 可以放一个默认的分享图片
				success: function(res) {
					// 分享成功的回调
					uni.showToast({
						title: '分享成功',
						icon: 'success'
					});
				},
				fail: function(res) {
					// 分享失败的回调
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					});
				}
			}
		}
		
		return {
			title: '法律法规详情',
			path: '/pages/legal/legal-list'
		}
	},
	methods: {
		loadLegalDetail() {
			// 模拟从API获取法律法规详情
			// 实际应用中，这里应该调用API获取数据
			setTimeout(() => {
				const allLegals = [
					{
						id: 1,
						title: '中华人民共和国老年人权益保障法',
						publishDate: '2018-12-29',
						source: '全国人民代表大会常务委员会',
						summary: '为了保障老年人合法权益，发展养老事业，弘扬中华民族敬老、养老、助老的美德，实现老有所养、老有所医、老有所为、老有所学、老有所乐，制定本法。',
						tags: ['法律', '老年人权益'],
						category: 1,
						content: '第一章 总则\n第一条 为了保障老年人合法权益，发展养老事业，弘扬中华民族敬老、养老、助老的美德，实现老有所养、老有所医、老有所为、老有所学、老有所乐，制定本法。\n第二条 本法所称老年人是指六十周岁以上的公民。\n第三条 老年人有从国家和社会获得物质帮助的权利，有享受社会服务和社会优待的权利，有参与社会发展和共享发展成果的权利。\n老年人的合法权益受法律保护，任何组织或者个人不得侵犯老年人合法权益。'
					},
					{
						id: 2,
						title: '养老机构管理办法',
						publishDate: '2020-11-01',
						source: '民政部',
						summary: '为了规范养老机构管理，保障入住老年人和养老机构合法权益，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律法规，制定本办法。',
						tags: ['行政法规', '养老机构'],
						category: 2,
						content: '第一章 总则\n第一条 为了规范养老机构管理，保障入住老年人和养老机构合法权益，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律法规，制定本办法。\n第二条 本办法所称养老机构是指依法办理登记，为老年人提供全日集中住宿和照料服务的机构。\n第三条 养老机构管理遵循政府监管、行业自律、社会监督相结合的原则。'
					},
					{
						id: 3,
						title: '上海市养老服务条例',
						publishDate: '2018-05-01',
						source: '上海市人民代表大会常务委员会',
						summary: '为了规范养老服务活动，保障老年人合法权益，满足老年人多样化、多层次养老服务需求，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律、行政法规，结合本市实际，制定本条例。',
						tags: ['地方性法规', '养老服务'],
						category: 3,
						content: '第一章 总则\n第一条 为了规范养老服务活动，保障老年人合法权益，满足老年人多样化、多层次养老服务需求，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律、行政法规，结合本市实际，制定本条例。\n第二条 本条例适用于本市行政区域内的养老服务及其相关活动。\n第三条 本市养老服务应当坚持政府主导、社会参与、市场运作、统筹发展的原则，构建以居家为基础、社区为依托、机构为支撑、医养相结合的养老服务体系。'
					},
					{
						id: 4,
						title: '上海市老年人权益保障条例',
						publishDate: '2016-01-01',
						source: '上海市人民代表大会常务委员会',
						summary: '为了保障老年人合法权益，发展本市老龄事业，弘扬中华民族敬老、养老、助老的美德，根据《中华人民共和国老年人权益保障法》和其他有关法律、行政法规，结合本市实际情况，制定本条例。',
						tags: ['地方性法规', '老年人权益'],
						category: 3,
						content: '第一章 总则\n第一条 为了保障老年人合法权益，发展本市老龄事业，弘扬中华民族敬老、养老、助老的美德，根据《中华人民共和国老年人权益保障法》和其他有关法律、行政法规，结合本市实际情况，制定本条例。\n第二条 本条例适用于本市行政区域内老年人权益保障及相关活动。\n第三条 本条例所称老年人是指六十周岁以上的本市户籍居民。'
					},
					{
						id: 5,
						title: '宝山区养老服务促进办法',
						publishDate: '2019-07-01',
						source: '宝山区人民政府',
						summary: '为了促进本区养老服务发展，满足老年人多层次、多样化的养老服务需求，根据《上海市养老服务条例》等法律法规，结合本区实际，制定本办法。',
						tags: ['规章', '养老服务'],
						category: 4,
						content: '第一章 总则\n第一条 为了促进本区养老服务发展，满足老年人多层次、多样化的养老服务需求，根据《上海市养老服务条例》等法律法规，结合本区实际，制定本办法。\n第二条 本办法适用于本区行政区域内的养老服务及其相关活动。\n第三条 本区养老服务应当坚持政府主导、社会参与、市场运作、统筹发展的原则，构建以居家为基础、社区为依托、机构为支撑、医养相结合的养老服务体系。'
					},
					{
						id: 6,
						title: '关于加强医养结合促进健康养老服务发展的指导意见',
						publishDate: '2019-11-20',
						source: '国家卫生健康委员会、民政部',
						summary: '为贯彻落实党中央、国务院关于实施健康中国战略和积极应对人口老龄化国家战略的决策部署，进一步深化医养结合，提升健康养老服务能力和水平，满足老年人健康养老服务需求，现提出以下意见。',
						tags: ['规章', '医养结合'],
						category: 4,
						content: '一、总体要求\n（一）指导思想。以习近平新时代中国特色社会主义思想为指导，全面贯彻党的十九大和十九届二中、三中、四中全会精神，认真落实党中央、国务院决策部署，牢固树立新发展理念，深入推进医养结合，建立健全医养结合政策法规体系、标准规范体系、管理服务体系，统筹医疗卫生与养老服务资源，满足老年人健康养老服务需求，提高老年人健康水平和生活质量，努力构建居家社区机构相协调、医养康养相结合的健康养老服务体系。'
					},
					{
						id: 7,
						title: '关于推进养老服务发展的意见',
						publishDate: '2019-04-16',
						source: '国务院办公厅',
						summary: '为深入贯彻落实党的十九大精神，持续深化养老服务改革，进一步扩大养老服务供给，提升养老服务质量，加快养老服务发展，更好满足老年人养老服务需求，现提出以下意见。',
						tags: ['规章', '养老服务'],
						category: 4,
						content: '一、深化放管服改革\n（一）简化养老机构设立流程。制定养老机构登记备案办法，实施养老机构登记备案制度，做好与现行许可制度的衔接。养老机构由民政部门实施统一登记备案，不再单独制发养老机构设立许可证。民政部门和其他有关部门应当在各自职责范围内对养老机构实施监督检查，减少多头检查。'
					},
					{
						id: 8,
						title: '中华人民共和国社会保险法',
						publishDate: '2011-07-01',
						source: '全国人民代表大会常务委员会',
						summary: '为了规范社会保险关系，维护公民参加社会保险和享受社会保险待遇的合法权益，使公民共享发展成果，促进社会和谐稳定，根据宪法，制定本法。',
						tags: ['法律', '社会保险'],
						category: 1,
						content: '第一章 总则\n第一条 为了规范社会保险关系，维护公民参加社会保险和享受社会保险待遇的合法权益，使公民共享发展成果，促进社会和谐稳定，根据宪法，制定本法。\n第二条 国家建立基本养老保险、基本医疗保险、工伤保险、失业保险、生育保险等社会保险制度，保障公民在年老、疾病、工伤、失业、生育等情况下依法从国家和社会获得物质帮助的权利。'
					},
					{
						id: 9,
						title: '中华人民共和国民法典',
						publishDate: '2021-01-01',
						source: '全国人民代表大会',
						summary: '为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序，适应中国特色社会主义发展要求，弘扬社会主义核心价值观，根据宪法，制定本法。',
						tags: ['法律', '民法典'],
						category: 1,
						content: '第一编 总则\n第一章 基本规定\n第一条 为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序，适应中国特色社会主义发展要求，弘扬社会主义核心价值观，根据宪法，制定本法。\n第二条 民法调整平等主体的自然人、法人和非法人组织之间的人身关系和财产关系。'
					},
					{
						id: 10,
						title: '上海市居家养老服务条例',
						publishDate: '2021-05-01',
						source: '上海市人民代表大会常务委员会',
						summary: '为了规范居家养老服务活动，保障老年人合法权益，促进居家养老服务健康发展，根据《中华人民共和国老年人权益保障法》《上海市养老服务条例》等法律、法规，结合本市实际，制定本条例。',
						tags: ['地方性法规', '居家养老'],
						category: 3,
						content: '第一章 总则\n第一条 为了规范居家养老服务活动，保障老年人合法权益，促进居家养老服务健康发展，根据《中华人民共和国老年人权益保障法》《上海市养老服务条例》等法律、法规，结合本市实际，制定本条例。\n第二条 本条例适用于本市行政区域内的居家养老服务及其相关活动。\n第三条 本条例所称居家养老服务，是指以家庭为基础，通过政府支持、社会参与，为居住在家的老年人提供的生活照料、医疗护理、精神慰藉、紧急救援等方面的服务。'
					}
				];
				
				this.legal = allLegals.find(legal => legal.id === this.legalId);
				
				if (!this.legal) {
					uni.showToast({
						title: '未找到相关法律法规',
						icon: 'none'
					});
				}
			}, 500);
		},
		loadRelatedLegals() {
			// 模拟获取相关法律法规
			setTimeout(() => {
				if (!this.legal) return;
				
				const allLegals = [
					{
						id: 1,
						title: '中华人民共和国老年人权益保障法',
						tags: ['法律', '老年人权益']
					},
					{
						id: 2,
						title: '养老机构管理办法',
						tags: ['行政法规', '养老机构']
					},
					{
						id: 3,
						title: '上海市养老服务条例',
						tags: ['地方性法规', '养老服务']
					},
					{
						id: 4,
						title: '上海市老年人权益保障条例',
						tags: ['地方性法规', '老年人权益']
					},
					{
						id: 5,
						title: '宝山区养老服务促进办法',
						tags: ['规章', '养老服务']
					}
				];
				
				// 筛选出与当前法律法规相关的法律法规（共享至少一个标签，但不是当前法律法规）
				this.relatedLegals = allLegals.filter(l => 
					l.id !== this.legalId && 
					l.tags.some(tag => this.legal.tags.includes(tag))
				).slice(0, 3); // 最多显示3个相关法律法规
			}, 500);
		},
		loadLegalInterpretation() {
			// 模拟获取法律解读
			setTimeout(() => {
				if (!this.legal) return;
				
				// 根据法律法规ID提供不同的解读内容
				const interpretations = {
					1: `
						<h3>法律要点解读</h3>
						<p>《中华人民共和国老年人权益保障法》是我国保障老年人合法权益的基本法律，主要包括以下几个方面：</p>
						<p>1. 明确了老年人享有的基本权益；</p>
						<p>2. 规定了家庭成员赡养、扶养老年人的义务；</p>
						<p>3. 确立了社会保障制度对老年人的保障措施；</p>
						<p>4. 规定了社会服务和优待措施；</p>
						<p>5. 明确了老年人参与社会发展的权利。</p>
					`,
					2: `
						<h3>政策要点解读</h3>
						<p>《养老机构管理办法》是规范养老机构管理的重要行政法规，主要包括以下几个方面：</p>
						<p>1. 明确了养老机构的设立条件和程序；</p>
						<p>2. 规定了养老机构的服务内容和标准；</p>
						<p>3. 确立了养老机构的安全和质量管理要求；</p>
						<p>4. 规定了养老机构的监督管理机制。</p>
					`,
					3: `
						<h3>条例要点解读</h3>
						<p>《上海市养老服务条例》是上海市规范养老服务的地方性法规，主要包括以下几个方面：</p>
						<p>1. 明确了养老服务体系的构建原则和目标；</p>
						<p>2. 规定了居家养老、社区养老和机构养老的服务内容和标准；</p>
						<p>3. 确立了养老服务的政府支持措施；</p>
						<p>4. 规定了养老服务的监督管理机制。</p>
					`
				};
				
				this.legalInterpretation = interpretations[this.legalId] || '';
			}, 500);
		},
		viewLegalDetail(id) {
			// 如果是当前法律法规，不做跳转
			if (id === this.legalId) return;
			
			// 跳转到新的法律法规详情页
			uni.navigateTo({
				url: `/pages/legal/legal-detail?id=${id}`
			});
		},
		makePhoneCall(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber: phoneNumber,
				success: () => {
					console.log('拨打电话成功');
				},
				fail: (err) => {
					console.log('拨打电话失败', err);
				}
			});
		},
		shareLegal() {
			uni.showToast({
				title: '分享功能开发中',
				icon: 'none'
			});
		},
		saveLegal() {
			this.isSaved = !this.isSaved;
			uni.showToast({
				title: this.isSaved ? '已收藏' : '已取消收藏',
				icon: 'success'
			});
		},
		printLegal() {
			uni.showToast({
				title: '打印功能开发中',
				icon: 'none'
			});
		},
		voiceSpeak(voiceUrl) {
			if(voiceUrl === '' || voiceUrl === null || voiceUrl === undefined)
			{
				uni.showToast({
					title: '暂无播报语音',
					icon: 'none'
				});
				return;
			}
			const innerAudioContext = uni.createInnerAudioContext();
			innerAudioContext.autoplay = true;
			innerAudioContext.src = voiceUrl;
			innerAudioContext.onPlay(() => {
				console.log('开始播放');
			});
			innerAudioContext.onError((res) => {
				console.log(res.errMsg);
				console.log(res.errCode);
			});
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 60px; /* 为底部操作栏留出空间 */
}

.legal-detail {
	padding: 15px;
	background-color: white;
	margin-bottom: 15px;
}

.legal-title {
	font-size: 18px;
	font-weight: bold;
	line-height: 1.4;
	margin-bottom: 15px;
}

.legal-meta {
	display: flex;
	margin-bottom: 15px;
}

.meta-item {
	display: flex;
	align-items: center;
	font-size: 12px;
	color: #999;
	margin-right: 15px;
}

.legal-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20px;
}

.legal-tag {
	background-color: #f5f5f5;
	color: #666;
	font-size: 12px;
	padding: 4px 8px;
	border-radius: 4px;
	margin-right: 8px;
	margin-bottom: 8px;
}

.legal-content {
	font-size: 14px;
	line-height: 1.6;
	color: #333;
	margin-bottom: 30px;
}

.legal-content h3 {
	font-size: 16px;
	font-weight: bold;
	margin: 20px 0 10px;
	color: #333;
}

.legal-content p {
	margin-bottom: 10px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
	padding-left: 10px;
	border-left: 3px solid #C8A287;
}

.related-legals {
	background-color: white;
	padding: 15px;
	margin-bottom: 15px;
}

.related-legal-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.related-legal-item:last-child {
	border-bottom: none;
}

.related-legal-title {
	font-size: 14px;
	color: #333;
	flex: 1;
	margin-right: 10px;
}

.legal-interpretation {
	background-color: white;
	padding: 15px;
	margin-bottom: 15px;
}

.interpretation-content {
	font-size: 14px;
	line-height: 1.6;
	color: #333;
}

.consultation-methods {
	background-color: white;
	padding: 15px;
	margin-bottom: 15px;
}

.consultation-item {
	display: flex;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.consultation-item:last-child {
	border-bottom: none;
}

.consultation-icon {
	margin-right: 15px;
}

.consultation-info {
	flex: 1;
}

.consultation-title {
	font-size: 14px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
}

.consultation-detail {
	font-size: 12px;
	color: #666;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background-color: white;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: transparent;
	border: none;
	font-size: 12px;
	color: #333;
	padding: 5px 0;
}

.action-btn::after {
	border: none;
}

.action-btn text {
	margin-top: 5px;
}

.active {
	color: #C8A287;
}

.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}

.ml-1 {
	margin-left: 4px;
}
</style>
