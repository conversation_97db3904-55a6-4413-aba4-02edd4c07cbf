<template>
	<view class="container">
		<!-- 导航头部 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<my-icon type="arrow-left" size="24" color="#333"></my-icon>
			</view>
			<view class="page-title">机构详情</view>
			<view style="width: 24px;"></view>
		</view> -->

		<!-- 加载中提示 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 机构详情内容 -->
		<view class="detail-container" v-else-if="institution">
			<!-- 机构图片轮播 -->
			<swiper class="image-swiper" indicator-dots autoplay circular :interval="3000" :duration="500">
				<swiper-item v-if="institution.images && institution.images.length > 0" v-for="(image, index) in institution.images" :key="index">
					<image :src="image" mode="aspectFill" class="institution-image"></image>
				</swiper-item>
				<swiper-item v-else>
					<image :src="institution.image || defaultImage" mode="aspectFill" class="institution-image"></image>
				</swiper-item>
			</swiper>

			<!-- 机构基本信息 -->
			<view class="info-section">
				<view class="institution-name">{{institution.name}}</view>
				<!-- <view class="institution-rating">
					<my-icon type="star" size="16" color="#ff9500"></my-icon>
					<text class="rating-text">{{institution.rating || '暂无评分'}}</text>
				</view> -->

				<!-- 机构标签 -->
				<view class="institution-tags" v-if="institution.tags && institution.tags.length > 0">
					<view class="institution-tag" v-for="(tag, index) in institution.tags" :key="index">
						{{tag}}
					</view>
				</view>

				<!-- 机构详细信息 -->
				<view class="info-list">
					<view class="info-item">
						<my-icon type="location" size="20" color="#c8a287"></my-icon>
						<text class="info-text">{{institution.address || '暂无地址信息'}}</text>
					</view>
					<view class="info-item">
						<my-icon type="phone" size="20" color="#c8a287"></my-icon>
						<text class="info-text" @click="callPhone(institution.phone)">{{institution.phone || '暂无联系电话'}}</text>
					</view>
					<view class="info-item">
						<my-icon type="time" size="20" color="#c8a287"></my-icon>
						<text class="info-text">{{institution.time || '暂无营业时间'}}</text>
					</view>
					<view class="info-item" v-if="institution.price">
						<my-icon type="money" size="20" color="#c8a287"></my-icon>
						<text class="info-text">{{institution.price}}</text>
					</view>
				</view>
			</view>

			<!-- 机构介绍 -->
			<view class="description-section" v-if="institution.description">
				<view class="section-title">机构介绍</view>
				<view class="description-content">{{institution.description}}</view>
			</view>

			<!-- 服务项目 -->
			<view class="services-section" v-if="institution.services && institution.services.length > 0">
				<view class="section-title">服务项目</view>
				<view class="services-list">
					<view class="service-item" v-for="(service, index) in institution.services" :key="index">
						<view class="service-name">{{service.name}}</view>
						<view class="service-price" v-if="service.price">{{service.price}}</view>
					</view>
				</view>
			</view>

			<!-- 设施服务 -->
			<view class="facilities-section" v-if="institution.facilities && institution.facilities.length > 0">
				<view class="section-title">设施服务</view>
				<view class="facilities-list">
					<view class="facility-item" v-for="(facility, index) in institution.facilities" :key="index">
						<my-icon :type="facility.icon || 'check'" size="16" color="#c8a287"></my-icon>
						<text class="facility-name">{{facility.name}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 无数据提示 -->
		<view class="no-data" v-else>
			<image src="/static/images/no-data.png" class="no-data-image"></image>
			<text class="no-data-text">暂无机构信息</text>
		</view>

		<!-- 底部操作栏 -->
		<view class="action-bar" v-if="institution">
			<view class="action-button" @click="navigateToLocation">
				<my-icon type="location" size="24" color="#c8a287"></my-icon>
				<text class="action-text">导航</text>
			</view>
			<view class="action-button" @click="callPhone(institution.phone)">
				<my-icon type="phone" size="24" color="#c8a287"></my-icon>
				<text class="action-text">电话</text>
			</view>
			<button class="action-button primary" open-type="share">
				<text class="action-text-primary">分享服务</text>
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			id: null,
			type: null,
			loading: true,
			institution: null,
			defaultImage: 'https://images.unsplash.com/photo-1555992336-fb0d29498b13?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
		}
	},
	onLoad(options) {
		// 获取传递的参数
		this.id = options.id;
		this.type = options.type || '';

		// 加载机构详情
		this.getInstitutionDetail();
	},
	// 分享功能
	onShareAppMessage() {
		return {
			title: this.institution ? this.institution.name : '养老机构详情',
			path: `/pages/institution-detail/institution-detail?id=${this.id}&type=${this.type}`,
			imageUrl: this.institution && this.institution.image ? this.institution.image : this.defaultImage
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		getInstitutionDetail() {
			this.loading = true;

			// 构建请求参数
			let params = {
				id: this.id
			};

			// 如果有类型参数，添加到请求中
			if (this.type) {
				params.type = this.type;
			}

			// 发送请求获取机构详情
			uni.request({
				url: this.$backUrl + '/biz/sysinformationdisplay/detail-out',
				data: params,
				header: {},
				success: (res) => {
					console.log(res.data);
					if (res.data && res.data.data) {
						// 处理返回的机构详情数据
						const data = res.data.data;
						this.institution = {
							id: data.id,
							name: data.sfdname || '未命名机构',
							image: data.sfdimg || this.defaultImage,
							images: data.sfdimages ? data.sfdimages.split(',') : [data.sfdimg || this.defaultImage],
							rating: data.sfdrating || '4.5',
							address: data.sfdaddress || '暂无地址信息',
							phone: data.sfduphone || '暂无联系电话',
							time: data.sfdtime || '营业时间: 08:00-20:00',
							price: data.sfdprice || '',
							tags: data.sfdtags ? data.sfdtags.split(',') : [],
							description: data.sfdintroduce || '暂无机构介绍',
							services: this.parseServices(data.sfdservices),
							facilities: this.parseFacilities(data.sfdfacilities),
							latitude: data.sfdlatitude,
							longitude: data.sfdlongitude
						};
					} else {
						// 没有返回数据
						this.institution = null;
						uni.showToast({
							title: '获取机构详情失败',
							icon: 'none',
							duration: 2000
						});
					}
				},
				fail: (err) => {
					console.error('获取机构详情失败', err);
					this.institution = null;
					uni.showToast({
						title: '获取机构详情失败',
						icon: 'none',
						duration: 2000
					});
				},
				complete: () => {
					this.loading = false;
				}
			});
		},
		// 解析服务项目数据
		parseServices(servicesStr) {
			if (!servicesStr) return [];

			try {
				// 尝试解析JSON格式的服务项目
				return JSON.parse(servicesStr);
			} catch (e) {
				// 如果解析失败，尝试按逗号分隔
				return servicesStr.split(',').map(item => {
					return { name: item.trim() };
				});
			}
		},
		// 解析设施服务数据
		parseFacilities(facilitiesStr) {
			if (!facilitiesStr) return [];

			try {
				// 尝试解析JSON格式的设施服务
				return JSON.parse(facilitiesStr);
			} catch (e) {
				// 如果解析失败，尝试按逗号分隔
				return facilitiesStr.split(',').map(item => {
					return { name: item.trim(), icon: 'check' };
				});
			}
		},
		// 拨打电话
		callPhone(phone) {
			if (phone && phone !== '暂无联系电话') {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			} else {
				uni.showToast({
					title: '暂无联系电话',
					icon: 'none'
				});
			}
		},
		// 导航到机构位置
		navigateToLocation() {
			if (this.institution && this.institution.latitude && this.institution.longitude) {
				uni.openLocation({
					latitude: parseFloat(this.institution.latitude),
					longitude: parseFloat(this.institution.longitude),
					name: this.institution.name,
					address: this.institution.address || '暂无地址信息'
				});
			} else {
				uni.showToast({
					title: '暂无位置信息',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 70px; /* 为底部操作栏留出空间 */
	background-color: #f8f5f2;
	min-height: 100vh;
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

/* 加载中样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.loading-spinner {
	width: 40px;
	height: 40px;
	border: 3px solid #f3f3f3;
	border-top: 3px solid #c8a287;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 10px;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 14px;
	color: #999;
}

/* 机构图片轮播 */
.image-swiper {
	height: 250px;
	width: 100%;
}

.institution-image {
	width: 100%;
	height: 100%;
}

/* 机构基本信息 */
.info-section {
	background-color: white;
	padding: 15px;
	margin-bottom: 10px;
	border-radius: 10px;
	margin: 15px;
}

.institution-name {
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 10px;
}

.institution-rating {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.rating-text {
	margin-left: 5px;
	color: #ff9500;
}

.institution-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 15px;
}

.institution-tag {
	background-color: #f5f5f5;
	padding: 4px 10px;
	border-radius: 15px;
	font-size: 12px;
	color: #666;
}

.info-list {
	margin-top: 15px;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}

.info-text {
	margin-left: 10px;
	color: #333;
	font-size: 14px;
}

/* 机构介绍 */
.description-section, .services-section, .facilities-section {
	background-color: white;
	padding: 15px;
	margin: 0 15px 15px;
	border-radius: 10px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
	position: relative;
	padding-left: 10px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 2px;
	height: 16px;
	width: 3px;
	background-color: #c8a287;
	border-radius: 3px;
}

.description-content {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
}

/* 服务项目 */
.services-list {
	margin-top: 10px;
}

.service-item {
	display: flex;
	justify-content: space-between;
	padding: 10px 0;
	border-bottom: 1px solid #f5f5f5;
}

.service-item:last-child {
	border-bottom: none;
}

.service-name {
	font-size: 14px;
	color: #333;
}

.service-price {
	font-size: 14px;
	color: #c8a287;
}

/* 设施服务 */
.facilities-list {
	display: flex;
	flex-wrap: wrap;
	margin-top: 10px;
}

.facility-item {
	display: flex;
	align-items: center;
	width: 50%;
	margin-bottom: 10px;
}

.facility-name {
	margin-left: 5px;
	font-size: 14px;
	color: #666;
}

/* 底部操作栏 */
.action-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	background-color: white;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-button {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	flex: 1;
}

.action-text {
	font-size: 12px;
	color: #666;
	margin-top: 5px;
}

.action-button.primary {
	background-color: #c8a287;
	color: white;
	border-radius: 20px;
	padding: 10px 0;
	margin-left: 15px;
	flex: 2;
}

.action-text-primary {
	color: white;
	font-size: 14px;
}

/* 无数据提示 */
.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-data-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-data-text {
	font-size: 14px;
	color: #999;
}
</style>
