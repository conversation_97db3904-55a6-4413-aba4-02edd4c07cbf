<template>
	<text :class="['my-icon', 'icon-' + type]" :style="{ fontSize: size + 'px', color: color }"></text>
</template>

<script>
	export default {
		name: 'my-icon',
		props: {
			type: {
				type: String,
				default: ''
			},
			size: {
				type: [Number, String],
				default: 16
			},
			color: {
				type: String,
				default: '#333'
			}
		}
	}
</script>

<style>
	@font-face {
		font-family: "myicon";
		src: url('https://kodo.dingsd115.com/bs-uniapp/iconfont.ttf') format('truetype');
	}
	
	.my-icon {
		font-family: "myicon";
		font-size: 16px;
		font-style: normal;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}
	
	.icon-home:before { content: "\ef10"; }
	.icon-search:before { content: "\E8FE"; }
	.icon-user:before { content: "\Eaf7"; }
	.icon-settings:before { content: "\e830"; }
	.icon-map:before { content: "\ef12"; }
	.icon-food:before { content: "\e6c2"; }
	.icon-car:before { content: "\EF14"; }
	.icon-phone:before { content: "\e9a8"; }
	.icon-heart:before { content: "\e886"; }
	.icon-star:before { content: "\ea70"; }
	.icon-location:before { content: "\e8fb"; }
	.icon-location-arrow:before {
		content: "\e8f3";
	}
	.icon-clock:before { content: "\e72b"; }
	.icon-calendar:before { content: "\ef15"; }
	.icon-bell:before { content: "\E814"; }
	.icon-info:before { content: "\E815"; }
	.icon-plus:before { content: "\e9af"; }
	.icon-minus:before { content: "\e929"; }
	.icon-check:before { content: "\E818"; }
	.icon-close:before { content: "\e754"; }
	.icon-arrow-left:before { content: "\e60b"; }
	.icon-arrow-right:before { content: "\e64f"; }
	.icon-arrow-up:before { content: "\E822"; }
	.icon-arrow-down:before { content: "\E823"; }
	.icon-share:before { content: "\ea1f"; }
	.icon-edit:before { content: "\E825"; }
	.icon-delete:before { content: "\E826"; }
	.icon-wifi:before { content: "\E827"; }
	.icon-signal:before { content: "\E828"; }
	.icon-battery:before { content: "\E829"; }
	.icon-mic:before { content: "\E830"; }
	.icon-more:before { content: "\E831"; }
	.icon-right:before { content: "\E832"; }
	.icon-down:before { content: "\E833"; }
	.icon-up:before { content: "\E834"; }
	.icon-directions:before { content: "\E835"; }
	.icon-calendar-check:before { content: "\ef15"; }
	.icon-file:before { content: "\ef03"; }
	.icon-medical:before { content: "\EF02"; }
	.icon-marker:before { content: "\E838"; }
	.icon-phone-alt:before { content: "\E839"; }
	.icon-comment:before { content: "\E840"; }
	.icon-info-circle:before { content: "\E841"; }
	.icon-cog:before { content: "\E842"; }
	.icon-notification:before { content: "\E843"; }
	.icon-restaurant:before { content: "\EF07"; }
	.icon-bus:before { content: "\F207"; }
	.icon-stethoscope:before { content: "\E845"; }
	.icon-broom:before { content: "\EF18"; }
	.icon-hands-helping:before { content: "\ef01"; }
	.icon-first-aid:before { content: "\E848"; }
	.icon-navigation:before { content: "\E849"; }
	.icon-legal:before { content: "\ef06"; }
	.icon-wheelchair:before { content: "\ef17"; }
	.icon-trash:before { content: "\E852"; }
	.icon-shop:before { content: "\E853"; }
	.icon-zyfw:before { content: "\ef08"; }
	.icon-yljg:before { content: "\ef05"; }
	.icon-bsjg:before { content: "\ef13"; }
	.icon-hdcg:before { content: "\ef04"; }
	.icon-zlss:before { content: "\ef16"; }
	.icon-kjss:before { content: "\ef11"; }
	.icon-zxzx:before { content: "\ef09"; }
	.icon-ylzx:before { content: "\ef19"; }
	.icon-people:before { content: "\e978"; }
	.icon-print:before { content: "\e9cb"; }
	.icon-play:before { content: "\e9b4"; }
	.icon-pause:before { content: "\e966"; }
	.icon-speak:before { content: "\e881"; }
	.icon-person-cane:before { content: "\e97d";}
</style>
