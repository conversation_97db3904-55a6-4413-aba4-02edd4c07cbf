<template>
	<main-layout>
	<view class="container">
		<!-- 状态栏 -->
		<!-- <view class="status-bar">
			<view>{{currentTime}}</view>
			<view>
				<my-icon type="signal" size="18"></my-icon>
				<my-icon type="wifi" size="18" class="ml-2"></my-icon>
				<my-icon type="battery" size="18" class="ml-2"></my-icon>
			</view>
		</view> -->

		<!-- 应用头部 -->
		<view class="app-header">
			<view class="w-8 h-8 rounded-full overflow-hidden" @click="navigateTo('/pages/user/user')">
				<image :src="userInfo.avatar || '/static/images/avatar/avatar.svg'" alt="头像" class="w-full h-full"></image>
			</view>
			<view class="search-bar" style="padding: 0;">
				<view class="search-input-container">
						<my-icon type="search" size="18" color="#9ca3af" class="search-icon"></my-icon>
						<input
							class="search-input"
							type="text"
							v-model="searchKeyword"
							placeholder="请输入关键字进行搜索"
							confirm-type="search"
							@confirm="confirmSearch"
						/>
						<view class="search-clear" v-if="searchKeyword" @click="clearSearch">
							<my-icon type="close" size="18" color="#9ca3af"></my-icon>
						</view>
				</view>
			</view>
		</view>

		<!-- Banner --> <!-- linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), -->
		<view class="banner" style="background-image: url('https://kodo.dingsd115.com/bs-uniapp/banner-allservices.png');">
			<!-- <view class="banner-text">所有服务</view> -->
		</view>

		<!-- 服务网格 -->
		<view class="service-grid-all">
			<view class="service-item-all" @click="navigateTo('/pages/policy/policy')">
				<view class="service-icon-all">
					<my-icon type="file" size="32" color="#c8a287"></my-icon>
				</view>
				<view>养老政策</view>
			</view>
			<!-- <view class="service-item-all" @click="navigateTo('/pages/legal/legal')">
				<view class="service-icon-all">
					<my-icon type="legal" size="32" color="#c8a287"></my-icon>
				</view>
				<view>法律法规</view>
			</view> -->
			<view class="service-item-all" @click="navigateTo('/pages/elderly-news/news-list')">
				<view class="service-icon-all">
					<my-icon type="ylzx" size="32" color="#c8a287"></my-icon>
				</view>
				<view>养老资讯</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/activity/activity-list')" ><!-- @click="navigateTo('/pages/activities/activities')" -->
				<view class="service-icon-all">
					<my-icon type="calendar" size="32" color="#c8a287"></my-icon>
				</view>
				<view>精彩活动</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/model-room/model-room')"><!-- @click="navigateTo('/pages/model-room/model-room')" -->
				<view class="service-icon-all">
					<my-icon type="home" size="32" color="#c8a287"></my-icon>
				</view>
				<view>养老样板间</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/assistive-devices/assistive-devices')" -->
				<view class="service-icon-all">
					<my-icon type="wheelchair" size="32" color="#c8a287"></my-icon>
				</view>
				<view>辅具展示</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/digital-map/digital-map')">
				<view class="service-icon-all">
					<my-icon type="map" size="32" color="#c8a287"></my-icon>
				</view>
				<view>数字地图</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/meal-service/meal-service')"><!-- @click="navigateTo('/pages/meal-service/meal-service')" -->
				<view class="service-icon-all">
					<my-icon type="restaurant" size="32" color="#c8a287"></my-icon>
				</view>
				<view>用餐服务</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/medical-service/medical-service')"><!-- @click="navigateTo('/pages/medical-service/medical-service')" -->
				<view class="service-icon-all">
					<my-icon type="medical" size="32" color="#c8a287"></my-icon>
				</view>
				<view>就医服务</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/transportation-service/transportation-service')" -->
				<view class="service-icon-all">
					<my-icon type="car" size="32" color="#c8a287"></my-icon>
				</view>
				<view>出行服务</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/homecare-service/homecare-service')" -->
				<view class="service-icon-all">
					<my-icon type="hands-helping" size="32" color="#c8a287"></my-icon>
				</view>
				<view>居家护理</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/cleaning-service/cleaning-service')" -->
				<view class="service-icon-all">
					<my-icon type="broom" size="32" color="#c8a287"></my-icon>
				</view>
				<view>保洁服务</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/yljg/yljg')" ><!-- @click="navigateTo('/pages/emergency-service/emergency-service')" -->
				<view class="service-icon-all">
					<my-icon type="yljg" size="32" color="#c8a287"></my-icon>
				</view>
				<view>养老机构</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/emergency-service/emergency-service')" -->
				<view class="service-icon-all">
					<my-icon type="zyfw" size="32" color="#c8a287"></my-icon>
				</view>
				<view>助浴服务</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/emergency-service/emergency-service')" -->
				<view class="service-icon-all">
					<my-icon type="hdcg" size="32" color="#c8a287"></my-icon>
				</view>
				<view>活动场馆</view>
			</view>
			<view class="service-item-all" @click="navigateTo('/pages/bsjg/bsjg')" ><!-- @click="navigateTo('/pages/emergency-service/emergency-service')" -->
				<view class="service-icon-all">
					<my-icon type="bsjg" size="32" color="#c8a287"></my-icon>
				</view>
				<view>办事机构</view>
			</view>
			<view class="service-item-all" @click="showServiceInProgress()"><!-- @click="navigateTo('/pages/emergency-service/emergency-service')" -->
				<view class="service-icon-all">
					<my-icon type="kjss" size="32" color="#c8a287"></my-icon>
				</view>
				<view>康健设施</view>
			</view>
			<!-- <view class="service-item-all" @click="showServiceInProgress()">
				<view class="service-icon-all">
					<my-icon type="zlss" size="32" color="#c8a287"></my-icon>
				</view>
				<view>助老设施</view>
			</view> -->
		</view>
	</view>
	</main-layout>
</template>

<script>
import MainLayout from '../../components/main-layout/main-layout.vue';

export default {
	components: {
		MainLayout
	},
	data() {
		return {
			searchKeyword: '',
			userInfo: {}
		}
	},
	onLoad() {

	},
	onShow() {
		this.getUserInfo();
	},
	methods: {
		navigateTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		showServiceInProgress() {
			uni.showToast({
				title: '服务建设中',
				icon: 'none',
				duration: 2000
			});
		},
		// 显示搜索输入弹窗
		showSearchInput() {
			this.$refs.searchPopup.open();
		},
		// 关闭搜索输入弹窗
		closeSearchPopup() {
			this.$refs.searchPopup.close();
			this.searchKeyword = '';
		},
		// 清空搜索关键词
		clearSearch() {
			this.searchKeyword = '';
		},
		// 确认搜索
		confirmSearch() {
			if (!this.searchKeyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}

			// 关闭弹窗
			this.$refs.searchPopup.close();

			// 跳转到搜索结果页
			uni.navigateTo({
				url: `/pages/search/search-result?keyword=${encodeURIComponent(this.searchKeyword)}`
			});
		},
		checkLogin() {
			const token = uni.getStorageSync('token');
			if (!token) {
				return false;
			} else {
				return true;
			}
		},
		getUserInfo() {
			if(this.checkLogin()) {
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userInfo = JSON.parse(userInfo);
				}
			}
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.ml-2 {
	margin-left: 8px;
}

.w-8 {
	width: 32px;
}

.h-8 {
	height: 32px;
}

.rounded-full {
	border-radius: 9999px;
}

.overflow-hidden {
	overflow: hidden;
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

.text-2xl {
	font-size: 24px;
}

.text-gray-400 {
	color: #9ca3af;
}

.mr-2 {
	margin-right: 8px;
}

.ml-auto {
	margin-left: auto;
}

/* 所有服务页面特定样式 */
.banner {
	height: 80px;
	background-size: cover;
	background-position: center;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20px;
}

.banner-text {
	color: white;
	font-size: 24px;
	font-weight: bold;
	text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.service-grid-all {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15px;
	padding: 0 15px;
}

.service-item-all {
	background-color: white;
	border-radius: 10px;
	padding: 15px;
	display: flex;
	align-items: center;
	box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.service-icon-all {
	width: 40px;
	height: 40px;
	margin-right: 15px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #c8a287;
}

/* 搜索弹窗样式 */
.search-popup {
	background-color: white;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
	overflow: hidden;
}

.search-popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.search-popup-title {
	font-size: 16px;
	font-weight: bold;
}

.search-popup-close {
	font-size: 14px;
	color: #666;
}

.search-popup-body {
	padding: 20px 15px;
}

.search-input-container {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 20px;
	padding: 0 15px;
	height: 40px;
}

.search-icon {
	margin-right: 10px;
}

.search-input {
	flex: 1;
	height: 40px;
	font-size: 14px;
}

.search-clear {
	padding: 5px;
}

.search-popup-footer {
	padding: 0 15px 20px 15px;
}

.search-btn {
	width: 100%;
	height: 44px;
	line-height: 44px;
	background-color: #c8a287;
	color: white;
	border-radius: 22px;
	font-size: 16px;
}
</style>
