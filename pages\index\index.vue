<template>
	<main-layout>
	<view class="container">
		<!-- 状态栏 -->
		<!-- <view class="status-bar">
			<view>{{currentTime}}</view>
			<view>
				<my-icon type="signal" size="18"></my-icon>
				<my-icon type="wifi" size="18" class="ml-2"></my-icon>
				<my-icon type="battery" size="18" class="ml-2"></my-icon>
			</view>
		</view> -->

		<!-- 应用头部 -->
		<view class="app-header">
			<view class="w-8 h-8 rounded-full overflow-hidden" @click="navigateTo('/pages/user/user')">
				<image :src="userInfo.avatar || '/static/images/avatar/avatar.svg'" alt="头像" class="w-full h-full"></image>
			</view>
			<view class="search-bar" style="padding: 0;">
				<view class="search-input-container">
						<my-icon type="search" size="18" color="#9ca3af" class="search-icon"></my-icon>
						<input
							class="search-input"
							type="text"
							v-model="searchKeyword"
							placeholder="请输入关键字进行搜索"
							confirm-type="search"
							@confirm="confirmSearch"
						/>
						<view class="search-clear" v-if="searchKeyword" @click="clearSearch">
							<my-icon type="close" size="18" color="#9ca3af"></my-icon>
						</view>
				</view>
			</view>
		</view>

		<!-- Banner -->
		<view class="banner"> <!-- style="filter: brightness(0.7);" -->
			<image src="https://kodo.dingsd115.com/bs-uniapp/banner-index.png" alt="老灵光宝山区为老服务平台" class="w-full h-full"></image>
			<!-- <view class="banner-text">
				老龄逛 "老灵光"<br>
				宝山区为老服务平台正式上线
			</view> -->
		</view>

		<!-- 服务部分标题 -->
		<view class="text-center font-bold text-lg mb-4">为老服务</view>

		<!-- 服务网格 -->
		<view class="service-grid">
			<view class="service-item" @click="navigateTo('/pages/meal-service/meal-service')">
				<view class="service-icon" style="background-color: #BE957E;">
					<my-icon type="restaurant" size="48" color="#FDF3EA"></my-icon>
				</view>
				<view class="service-text">用餐服务</view>
			</view>
			<!-- @click="navigateTo('/pages/transportation-service/transportation-service')" -->
			<view class="service-item" @click="showServiceInProgress()">
				<view class="service-icon" style="background-color: #A99888;">
					<my-icon type="car" size="48" color="#FDF3EA"></my-icon>
				</view>
				<view class="service-text">出行服务</view>
			</view>
			<view class="service-item" @click="navigateTo('/pages/medical-service/medical-service')">
				<view class="service-icon" style="background-color: #BE957E;">
					<my-icon type="medical" size="48" color="#FDF3EA"></my-icon>
				</view>
				<view class="service-text">就医服务</view>
			</view>
			<view class="service-item" @click="showServiceInProgress()">
				<view class="service-icon" style="background-color: #A99888;">
					<my-icon type="broom" size="48" color="#FDF3EA"></my-icon>
				</view>
				<view class="service-text">保洁服务</view>
			</view>
			<view class="service-item" @click="showServiceInProgress()">
				<view class="service-icon" style="background-color: #BE957E;">
					<my-icon type="hands-helping" size="48" color="#FDF3EA"></my-icon>
				</view>
				<view class="service-text">居家护理</view>
			</view>
			<view class="service-item" @click="showServiceInProgress()"><!-- @click="switchTab('/pages/all-services/all-services')" -->
				<view class="service-icon" style="background-color: #A99888;">
					<my-icon type="zyfw" size="48" color="#FDF3EA"></my-icon>
				</view>
				<view class="service-text">助浴服务</view>
			</view>
		</view>

		<!-- 地图Banner -->
		<view class="map-banner" @click="navigateTo('/pages/digital-map/digital-map')" style="background-image: url('https://kodo.dingsd115.com/bs-uniapp/mapBack.png');">
			<!-- <view class="map-text">数字地图</view> -->
		</view>

		<!-- 活动部分 -->
		<view class="px-4 mt-6">
			<view class="flex justify-between items-center mb-4">
				<view class="font-bold text-lg">精彩活动</view>
				<view class="text-sm text-gray-500" @click="navigateTo('/pages/activity/activity-list')">查看更多 ></view>
			</view>



			<view class="bg-white rounded-lg overflow-hidden mb-4 shadow-sm" v-for="item in activityInfo" @click="goToDetail(item.id)">
				<image :src="item.serimg" mode="aspectFill" style="height: 150px;" class="w-full"></image>
				<view class="p-3">
					<view class="font-bold mb-1">{{item.sername}}</view>
					<view class="text-sm text-gray-500 mb-2">时间：{{item.seractivitystrdate}}</view>
					<view class="text-sm text-gray-500">地点：宝山区文化中心</view>
				</view>
			</view>

			<!-- <view class="bg-white rounded-lg overflow-hidden mb-4 shadow-sm">
				<image src="https://images.unsplash.com/photo-1493676304819-0d7a8d026dcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" mode="aspectFill" style="height: 150px;" class="w-full"></image>
				<view class="p-3">
					<view class="font-bold mb-1">老年人才艺展示</view>
					<view class="text-sm text-gray-500 mb-2">时间：2025年4月20日 10:00</view>
					<view class="text-sm text-gray-500">地点：宝山区老年活动中心</view>
				</view>
			</view> -->
		</view>
	</view>
	</main-layout>
</template>

<script>
//import MainLayout from '../../components/main-layout/main-layout.vue';

export default {
	/* components: {
		MainLayout
	}, */
	data() {
		return {
			activityInfo: [],
			searchKeyword: '',
			userInfo: {}
		}
	},
	onLoad() {
		//uni.clearStorageSync();
		this.getActivityInfo();
	},
	onShow() {
		this.getUserInfo();
	},
	methods: {
		updateTime() {
			const now = new Date();
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}`;
		},
		getActivityInfo() {
			uni.request({
			    url: this.$backUrl + '/biz/syseventregistration/page-out',
			    data: {
			    	current: 1,  // 当前页码
			    	size: 5  // 每页数据量
			    },
			    header: {},
			    success: (res) => {
					this.activityInfo = res.data.data.records;
			    }
			})
		},
		goToDetail(id) {
			uni.navigateTo({
				url: `/pages/activity/activity-detail?id=${id}`
			});
		},
		navigateTo(url) {
			uni.navigateTo({
				url: url
			});
		},
		switchTab(url) {
			uni.switchTab({
				url: url
			})
		},
		showServiceInProgress() {
			uni.showToast({
				title: '服务建设中',
				icon: 'none',
				duration: 2000
			});
		},
		// 清空搜索关键词
		clearSearch() {
			this.searchKeyword = '';
		},
		// 确认搜索
		confirmSearch() {
			if (!this.searchKeyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}

			// 关闭弹窗
			//this.$refs.searchPopup.close();

			// 跳转到搜索结果页
			uni.navigateTo({
				url: `/pages/search/search-result?keyword=${encodeURIComponent(this.searchKeyword)}`
			});
		},
		checkLogin() {
			const token = uni.getStorageSync('token');
			if (!token) {
				return false;
			} else {
				return true;
			}
		},
		getUserInfo() {
			if(this.checkLogin()) {
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userInfo = JSON.parse(userInfo);
				}
			}
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.ml-2 {
	margin-left: 8px;
}

.w-8 {
	width: 32px;
}

.h-8 {
	height: 32px;
}

.rounded-full {
	border-radius: 9999px;
}

.overflow-hidden {
	overflow: hidden;
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

.text-center {
	text-align: center;
}

.font-bold {
	font-weight: bold;
}

.text-lg {
	font-size: 18px;
}

.mb-4 {
	margin-bottom: 16px;
}

.text-gray-400 {
	color: #9ca3af;
}

.text-gray-500 {
	color: #6b7280;
}

.mr-2 {
	margin-right: 8px;
}

.ml-auto {
	margin-left: auto;
}

.px-4 {
	padding-left: 16px;
	padding-right: 16px;
}

.mt-6 {
	margin-top: 24px;
}

.flex {
	display: flex;
}

.justify-between {
	justify-content: space-between;
}

.items-center {
	align-items: center;
}

.text-sm {
	font-size: 14px;
}

.bg-white {
	background-color: white;
}

.rounded-lg {
	border-radius: 8px;
}

.shadow-sm {
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.p-3 {
	padding: 12px;
}

.mb-1 {
	margin-bottom: 4px;
}

.mb-2 {
	margin-bottom: 8px;
}

/* 服务网格样式 */
.service-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15px;
	padding: 0 15px;
}

.service-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	padding: 10px;
	border-radius: 10px;
}

.service-icon {
	width: 60px;
	height: 60px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
}

.service-text {
	color: #333;
	font-size: 14px;
	margin-top: 5px;
}

.search-input-container {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 20px;
	padding: 0 15px;
	height: 40px;
	width: 100%;
}

.search-icon {
	margin-right: 10px;
}

.search-input {
	flex: 1;
	height: 40px;
	font-size: 14px;
}

.search-clear {
	padding: 5px;
}

</style>
