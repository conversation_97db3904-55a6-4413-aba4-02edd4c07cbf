<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-container">
			<view class="search-bar">
				<my-icon type="search" size="18" color="#9ca3af" class="mr-2"></my-icon>
				<input type="text" placeholder="搜索法律法规关键词" v-model="searchKeyword" class="flex-1" />
				<my-icon v-if="searchKeyword" type="close" size="18" color="#9ca3af" class="ml-auto" @click="clearSearch"></my-icon>
			</view>
		</view>
		
		<!-- 法律法规分类标签 -->
		<view class="category-tabs">
			<view 
				v-for="(category, index) in categories" 
				:key="index" 
				class="category-tab" 
				:class="{'active': currentCategory === index}"
				@click="selectCategory(index)"
			>
				{{category.name}}
			</view>
		</view>
		
		<!-- 法律法规列表 -->
		<view class="legal-list">
			<view 
				v-for="(legal, index) in filteredLegals" 
				:key="index" 
				class="legal-item"
				@click="viewLegalDetail(legal.id)"
			>
				<view class="legal-title">{{legal.title}}</view>
				<view class="legal-meta">
					<view class="legal-date">发布时间：{{legal.publishDate}}</view>
					<view class="legal-source">来源：{{legal.source}}</view>
				</view>
				<view class="legal-summary">{{legal.summary}}</view>
				<view class="legal-footer">
					<view class="legal-tags">
						<view class="legal-tag" v-for="(tag, tagIndex) in legal.tags" :key="tagIndex">
							{{tag}}
						</view>
					</view>
					<view class="read-more">
						<text>查看详情</text>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view v-if="hasMoreLegals" class="load-more" @click="loadMoreLegals">
				加载更多
			</view>
			<view v-else class="no-more">
				没有更多法律法规了
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			currentCategory: 0,
			categories: [
				{ name: '全部', value: 0 },
				{ name: '法律', value: 1 },
				{ name: '行政法规', value: 2 },
				{ name: '地方性法规', value: 3 },
				{ name: '规章', value: 4 }
			],
			legals: [],
			page: 1,
			pageSize: 5,
			hasMoreLegals: true
		}
	},
	computed: {
		filteredLegals() {
			let result = this.legals;
			
			// 按关键词搜索
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.trim().toLowerCase();
				result = result.filter(legal => 
					legal.title.toLowerCase().includes(keyword) || 
					legal.summary.toLowerCase().includes(keyword) ||
					legal.tags.some(tag => tag.toLowerCase().includes(keyword))
				);
			}
			
			return result;
		}
	},
	methods: {
		clearSearch() {
			this.searchKeyword = '';
		},
		selectCategory(index) {
			this.currentCategory = index;
			this.page = 1;
			this.legals = [];
			this.hasMoreLegals = true;
			this.loadMoreLegals();
		},
		viewLegalDetail(id) {
			uni.navigateTo({
				url: `/pages/legal/legal-detail?id=${id}`
			});
		},
		loadMoreLegals() {
			if (this.hasMoreLegals) {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});
				
				// 模拟API请求延迟
				setTimeout(() => {
					// 模拟获取法律法规数据
					const newLegals = this.getMockLegals(this.page, this.pageSize, this.categories[this.currentCategory].value);
					
					if (this.page === 1) {
						// 第一页，直接替换数据
						this.legals = newLegals;
					} else {
						// 不是第一页，追加数据
						this.legals = [...this.legals, ...newLegals];
					}
					
					// 判断是否还有更多数据
					if (newLegals.length < this.pageSize) {
						this.hasMoreLegals = false;
					} else {
						// 页码加1，准备下次加载
						this.page++;
						this.hasMoreLegals = true;
					}
					
					// 隐藏加载中
					uni.hideLoading();
				}, 500);
			}
		},
		getMockLegals(page, pageSize, categoryValue) {
			// 模拟法律法规数据
			const allLegals = [
				{
					id: 1,
					title: '中华人民共和国老年人权益保障法',
					publishDate: '2018-12-29',
					source: '全国人民代表大会常务委员会',
					summary: '为了保障老年人合法权益，发展养老事业，弘扬中华民族敬老、养老、助老的美德，实现老有所养、老有所医、老有所为、老有所学、老有所乐，制定本法。',
					tags: ['法律', '老年人权益'],
					category: 1,
					content: '第一章 总则\n第一条 为了保障老年人合法权益，发展养老事业，弘扬中华民族敬老、养老、助老的美德，实现老有所养、老有所医、老有所为、老有所学、老有所乐，制定本法。\n第二条 本法所称老年人是指六十周岁以上的公民。\n第三条 老年人有从国家和社会获得物质帮助的权利，有享受社会服务和社会优待的权利，有参与社会发展和共享发展成果的权利。\n老年人的合法权益受法律保护，任何组织或者个人不得侵犯老年人合法权益。'
				},
				{
					id: 2,
					title: '养老机构管理办法',
					publishDate: '2020-11-01',
					source: '民政部',
					summary: '为了规范养老机构管理，保障入住老年人和养老机构合法权益，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律法规，制定本办法。',
					tags: ['行政法规', '养老机构'],
					category: 2,
					content: '第一章 总则\n第一条 为了规范养老机构管理，保障入住老年人和养老机构合法权益，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律法规，制定本办法。\n第二条 本办法所称养老机构是指依法办理登记，为老年人提供全日集中住宿和照料服务的机构。\n第三条 养老机构管理遵循政府监管、行业自律、社会监督相结合的原则。'
				},
				{
					id: 3,
					title: '上海市养老服务条例',
					publishDate: '2018-05-01',
					source: '上海市人民代表大会常务委员会',
					summary: '为了规范养老服务活动，保障老年人合法权益，满足老年人多样化、多层次养老服务需求，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律、行政法规，结合本市实际，制定本条例。',
					tags: ['地方性法规', '养老服务'],
					category: 3,
					content: '第一章 总则\n第一条 为了规范养老服务活动，保障老年人合法权益，满足老年人多样化、多层次养老服务需求，促进养老服务健康发展，根据《中华人民共和国老年人权益保障法》等法律、行政法规，结合本市实际，制定本条例。\n第二条 本条例适用于本市行政区域内的养老服务及其相关活动。\n第三条 本市养老服务应当坚持政府主导、社会参与、市场运作、统筹发展的原则，构建以居家为基础、社区为依托、机构为支撑、医养相结合的养老服务体系。'
				},
				{
					id: 4,
					title: '上海市老年人权益保障条例',
					publishDate: '2016-01-01',
					source: '上海市人民代表大会常务委员会',
					summary: '为了保障老年人合法权益，发展本市老龄事业，弘扬中华民族敬老、养老、助老的美德，根据《中华人民共和国老年人权益保障法》和其他有关法律、行政法规，结合本市实际情况，制定本条例。',
					tags: ['地方性法规', '老年人权益'],
					category: 3,
					content: '第一章 总则\n第一条 为了保障老年人合法权益，发展本市老龄事业，弘扬中华民族敬老、养老、助老的美德，根据《中华人民共和国老年人权益保障法》和其他有关法律、行政法规，结合本市实际情况，制定本条例。\n第二条 本条例适用于本市行政区域内老年人权益保障及相关活动。\n第三条 本条例所称老年人是指六十周岁以上的本市户籍居民。'
				},
				{
					id: 5,
					title: '宝山区养老服务促进办法',
					publishDate: '2019-07-01',
					source: '宝山区人民政府',
					summary: '为了促进本区养老服务发展，满足老年人多层次、多样化的养老服务需求，根据《上海市养老服务条例》等法律法规，结合本区实际，制定本办法。',
					tags: ['规章', '养老服务'],
					category: 4,
					content: '第一章 总则\n第一条 为了促进本区养老服务发展，满足老年人多层次、多样化的养老服务需求，根据《上海市养老服务条例》等法律法规，结合本区实际，制定本办法。\n第二条 本办法适用于本区行政区域内的养老服务及其相关活动。\n第三条 本区养老服务应当坚持政府主导、社会参与、市场运作、统筹发展的原则，构建以居家为基础、社区为依托、机构为支撑、医养相结合的养老服务体系。'
				},
				{
					id: 6,
					title: '关于加强医养结合促进健康养老服务发展的指导意见',
					publishDate: '2019-11-20',
					source: '国家卫生健康委员会、民政部',
					summary: '为贯彻落实党中央、国务院关于实施健康中国战略和积极应对人口老龄化国家战略的决策部署，进一步深化医养结合，提升健康养老服务能力和水平，满足老年人健康养老服务需求，现提出以下意见。',
					tags: ['规章', '医养结合'],
					category: 4,
					content: '一、总体要求\n（一）指导思想。以习近平新时代中国特色社会主义思想为指导，全面贯彻党的十九大和十九届二中、三中、四中全会精神，认真落实党中央、国务院决策部署，牢固树立新发展理念，深入推进医养结合，建立健全医养结合政策法规体系、标准规范体系、管理服务体系，统筹医疗卫生与养老服务资源，满足老年人健康养老服务需求，提高老年人健康水平和生活质量，努力构建居家社区机构相协调、医养康养相结合的健康养老服务体系。'
				},
				{
					id: 7,
					title: '关于推进养老服务发展的意见',
					publishDate: '2019-04-16',
					source: '国务院办公厅',
					summary: '为深入贯彻落实党的十九大精神，持续深化养老服务改革，进一步扩大养老服务供给，提升养老服务质量，加快养老服务发展，更好满足老年人养老服务需求，现提出以下意见。',
					tags: ['规章', '养老服务'],
					category: 4,
					content: '一、深化放管服改革\n（一）简化养老机构设立流程。制定养老机构登记备案办法，实施养老机构登记备案制度，做好与现行许可制度的衔接。养老机构由民政部门实施统一登记备案，不再单独制发养老机构设立许可证。民政部门和其他有关部门应当在各自职责范围内对养老机构实施监督检查，减少多头检查。'
				},
				{
					id: 8,
					title: '中华人民共和国社会保险法',
					publishDate: '2011-07-01',
					source: '全国人民代表大会常务委员会',
					summary: '为了规范社会保险关系，维护公民参加社会保险和享受社会保险待遇的合法权益，使公民共享发展成果，促进社会和谐稳定，根据宪法，制定本法。',
					tags: ['法律', '社会保险'],
					category: 1,
					content: '第一章 总则\n第一条 为了规范社会保险关系，维护公民参加社会保险和享受社会保险待遇的合法权益，使公民共享发展成果，促进社会和谐稳定，根据宪法，制定本法。\n第二条 国家建立基本养老保险、基本医疗保险、工伤保险、失业保险、生育保险等社会保险制度，保障公民在年老、疾病、工伤、失业、生育等情况下依法从国家和社会获得物质帮助的权利。'
				},
				{
					id: 9,
					title: '中华人民共和国民法典',
					publishDate: '2021-01-01',
					source: '全国人民代表大会',
					summary: '为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序，适应中国特色社会主义发展要求，弘扬社会主义核心价值观，根据宪法，制定本法。',
					tags: ['法律', '民法典'],
					category: 1,
					content: '第一编 总则\n第一章 基本规定\n第一条 为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序，适应中国特色社会主义发展要求，弘扬社会主义核心价值观，根据宪法，制定本法。\n第二条 民法调整平等主体的自然人、法人和非法人组织之间的人身关系和财产关系。'
				},
				{
					id: 10,
					title: '上海市居家养老服务条例',
					publishDate: '2021-05-01',
					source: '上海市人民代表大会常务委员会',
					summary: '为了规范居家养老服务活动，保障老年人合法权益，促进居家养老服务健康发展，根据《中华人民共和国老年人权益保障法》《上海市养老服务条例》等法律、法规，结合本市实际，制定本条例。',
					tags: ['地方性法规', '居家养老'],
					category: 3,
					content: '第一章 总则\n第一条 为了规范居家养老服务活动，保障老年人合法权益，促进居家养老服务健康发展，根据《中华人民共和国老年人权益保障法》《上海市养老服务条例》等法律、法规，结合本市实际，制定本条例。\n第二条 本条例适用于本市行政区域内的居家养老服务及其相关活动。\n第三条 本条例所称居家养老服务，是指以家庭为基础，通过政府支持、社会参与，为居住在家的老年人提供的生活照料、医疗护理、精神慰藉、紧急救援等方面的服务。'
				}
			];
			
			// 根据分类筛选
			let filteredLegals = allLegals;
			if (categoryValue !== 0) {
				filteredLegals = allLegals.filter(legal => legal.category === categoryValue);
			}
			
			// 计算分页
			const startIndex = (page - 1) * pageSize;
			const endIndex = startIndex + pageSize;
			
			return filteredLegals.slice(startIndex, endIndex);
		}
	},
	onLoad() {
		this.loadMoreLegals();
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.search-container {
	padding: 10px 15px;
	background-color: white;
	margin-bottom: 10px;
}

.search-bar {
	background-color: #f5f5f5;
	border-radius: 20px;
	padding: 8px 15px;
	display: flex;
	align-items: center;
}

.category-tabs {
	display: flex;
	overflow-x: auto;
	background-color: white;
	padding: 10px 15px;
	margin-bottom: 10px;
}

.category-tab {
	padding: 6px 12px;
	margin-right: 10px;
	border-radius: 16px;
	font-size: 14px;
	white-space: nowrap;
	background-color: #f5f5f5;
	color: #666;
}

.category-tab.active {
	background-color: #C8A287;
	color: white;
}

.legal-list {
	padding: 0 15px;
}

.legal-item {
	background-color: white;
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 15px;
}

.legal-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 10px;
	line-height: 1.4;
}

.legal-meta {
	display: flex;
	justify-content: space-between;
	font-size: 12px;
	color: #999;
	margin-bottom: 10px;
}

.legal-summary {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15px;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
}

.legal-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.legal-tags {
	display: flex;
	flex-wrap: wrap;
}

.legal-tag {
	display: inline-block;
	background-color: #f5f5f5;
	color: #666;
	font-size: 12px;
	padding: 4px 8px;
	border-radius: 4px;
	margin-right: 8px;
	margin-bottom: 5px;
}

.read-more {
	display: flex;
	align-items: center;
	font-size: 14px;
	color: #C8A287;
}

.load-more, .no-more {
	text-align: center;
	padding: 15px 0;
	font-size: 14px;
	color: #999;
}

.load-more {
	color: #C8A287;
}

.mr-2 {
	margin-right: 8px;
}

.ml-auto {
	margin-left: auto;
}

.ml-1 {
	margin-left: 4px;
}

.flex-1 {
	flex: 1;
}
</style>
