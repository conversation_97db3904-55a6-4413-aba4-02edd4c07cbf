<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<!-- <view class="nav-bar">
			<view class="nav-back" @click="goBack">
				<my-icon type="arrow-left" size="24" color="#333"></my-icon>
			</view>
			<view class="nav-title">适老样板间</view>
			<view class="nav-actions">
				<my-icon type="more" size="24" color="#333" class="mr-4"></my-icon>
				<my-icon type="share" size="24" color="#333"></my-icon>
			</view>
		</view> -->

		<!-- 轮播图 -->
		<swiper class="swiper" circular indicator-dots autoplay interval="3000" duration="500">
			<swiper-item v-for="(item, index) in swiperList" :key="index">
				<image :src="item.image" mode="aspectFill" class="swiper-image"></image>
			</swiper-item>
		</swiper>

		<!-- 内容标题 -->
		<view class="content-title">
			<text class="title">宜家家居宝山店 适老样板间设计简介</text>
		</view>

		<!-- 内容简介 -->
		<view class="content-intro">
			<text class="intro-text">
				随着老人年龄的增长,身体也逐渐出现一些变化,当年的家居布置和装修已不能满足现在的需求。通过对老人房间适老化改造装修,让老人的养老生活变的更舒适更安全。
			</text>
		</view>

		<!-- 功能区域，固定在底部 -->
		<view class="function-area">
			<view class="function-item">
				<image src="https://kodo.dingsd115.com/bs-uniapp/ikea-logo.png" mode="aspectFit" class="logo-image"></image>
				<text class="function-text">宜家适老业务请联系</text>
			</view>
			<view class="function-button" @click="contactConsultant">
				<text class="button-text">咨询专员</text>
			</view>
		</view>

		<!-- 样板间特点 -->
		<!-- <view class="features">
			<view class="section-title">样板间特点</view>

			<view class="feature-item">
				<view class="feature-icon">
					<my-icon type="info-circle" size="40" color="#4CAF50"></my-icon>
				</view>
				<view class="feature-content">
					<text class="feature-title">安全防护</text>
					<text class="feature-desc">全屋安装防滑地板、扶手和紧急呼叫系统，确保老人居住安全</text>
				</view>
			</view>

			<view class="feature-item">
				<view class="feature-icon">
					<my-icon type="heart" size="40" color="#4CAF50"></my-icon>
				</view>
				<view class="feature-content">
					<text class="feature-title">舒适便利</text>
					<text class="feature-desc">考虑老人行动不便，家具高度适中，开关位置便于操作</text>
				</view>
			</view>

			<view class="feature-item">
				<view class="feature-icon">
					<my-icon type="medical" size="40" color="#4CAF50"></my-icon>
				</view>
				<view class="feature-content">
					<text class="feature-title">健康环保</text>
					<text class="feature-desc">使用环保材料，良好通风设计，创造健康居住环境</text>
				</view>
			</view>

			<view class="feature-item">
				<view class="feature-icon">
					<my-icon type="cog" size="40" color="#4CAF50"></my-icon>
				</view>
				<view class="feature-content">
					<text class="feature-title">智能科技</text>
					<text class="feature-desc">集成智能家居系统，语音控制灯光、温度等，提高生活便利性</text>
				</view>
			</view>
		</view> -->

		<!-- 改造案例 -->
		<!-- <view class="cases">
			<view class="section-title">改造案例展示</view>

			<view class="case-grid">
				<view class="case-item" v-for="(item, index) in caseList" :key="index" @click="viewCaseDetail(item)">
					<image :src="item.image" mode="aspectFill" class="case-image"></image>
					<text class="case-title">{{item.title}}</text>
				</view>
			</view>
		</view> -->
		<!-- 咨询专员模态框 -->
		<view class="contact-modal" v-if="showContactModal">
			<view class="modal-mask" @click="closeContactModal"></view>
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">联系咨询专员</text>
					<view class="modal-close" @click="closeContactModal">
						<my-icon type="close" size="24" color="#333"></my-icon>
					</view>
				</view>
				<view class="modal-body">
					<view class="qrcode-container">
						<image src="https://kodo.dingsd115.com/bs-uniapp/consultant-qrcode.png" mode="aspectFit" class="qrcode-image" 
						<!-- #ifdef MP-WEIXIN -->
						:show-menu-by-longpress="true"
						<!-- #endif -->
						></image>
						<text class="qrcode-text">扫码添加咨询专员微信</text>
					</view>
					<view class="divider"></view>
					<view class="phone-container" @click="makePhoneCall">
						<my-icon type="phone" size="36" color="#0057a3"></my-icon>
						<text class="phone-number">13127765628</text>
					</view>
					<text class="service-time">服务时间: 周一至周五 9:00-18:00</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>

export default {
	data() {
		return {
			showContactModal: false,
			swiperList: [
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room1.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room2.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room3.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room4.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room5.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room6.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room7.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room8.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room9.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room10.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room11.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room12.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room13.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room14.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room15.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room16.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room17.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room18.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room19.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room20.jpg'
				},
				{
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room21.jpg'
				}
			],
			caseList: [
				/* {
					id: 1,
					title: '卧室适老化改造',
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room6.jpg',
					description: '增加床边扶手，调整床高，安装感应夜灯'
				},
				{
					id: 2,
					title: '卫浴间改造',
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room13.jpg',
					description: '安装防滑地砖，增加扶手，座便器高度调整'
				},
				{
					id: 3,
					title: '厨房适老化',
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room12.jpg',
					description: '降低操作台面，增加照明，安装易开启的抽屉'
				},
				{
					id: 4,
					title: '客厅改造',
					image: 'https://kodo.dingsd115.com/bs-uniapp/model-room11.jpg',
					description: '移除绊脚物，增加舒适座椅，优化照明系统'
				} */
			]
		}
	},
	onLoad() {
		// 页面加载时的逻辑
		//console.log('适老样板间页面加载完成');
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		contactConsultant() {
			// 显示包含二维码和电话信息的模态框
			this.showContactModal = true;
		},

		closeContactModal() {
			this.showContactModal = false;
		},

		makePhoneCall() {
			uni.makePhoneCall({
				phoneNumber: '13127765628'
			});
		},
		viewCaseDetail(item) {
			// 这里可以跳转到案例详情页，目前先用提示框展示
			console.log('查看案例详情:', item.title);
			uni.showToast({
				title: '案例详情功能开发中',
				icon: 'none'
			});
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 100rpx; /* 增加底部空间，为固定在底部的功能区域留出空间 */
	background-color: #f8f8f8;
}

.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background-color: #fff;
}

.nav-back {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-title {
	font-size: 36rpx;
	font-weight: bold;
}

.nav-actions {
	display: flex;
	align-items: center;
}

.mr-4 {
	margin-right: 16rpx;
}

.swiper {
	width: 100%;
	height: 800rpx;
}

.swiper-image {
	width: 100%;
	height: 100%;
}

.content-title {
	padding: 30rpx;
	background-color: #fff;
	margin-top: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.content-intro {
	padding: 30rpx;
	background-color: #fff;
	margin-top: 2rpx;
}

.intro-text {
	font-size: 30rpx;
	color: #666;
	line-height: 1.6;
}

.function-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	background-color: #0057a3; /* 宜家蓝色 */
	z-index: 100;
	box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.function-item {
	display: flex;
	align-items: center;
}

.logo-image {
	width: 80rpx;
	height: 40rpx;
	margin-right: 20rpx;
	/* filter: brightness(0) invert(1); */ /* 将logo变为白色 */
}

.function-text {
	font-size: 28rpx;
	color: #fff;
	font-weight: 500;
}

.function-button {
	background-color: #fff;
	color: #0057a3;
	padding: 15rpx 30rpx;
	border-radius: 30rpx;
}

.button-text {
	font-size: 28rpx;
	color: #0057a3;
	font-weight: bold;
}

.features {
	padding: 30rpx;
	background-color: #fff;
	margin-top: 20rpx;
}

.section-title {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.feature-item {
	display: flex;
	margin-bottom: 30rpx;
}

.feature-icon {
	margin-right: 20rpx;
}

.feature-content {
	flex: 1;
}

.feature-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	display: block;
}

.feature-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.cases {
	padding: 30rpx;
	background-color: #fff;
	margin-top: 20rpx;
}

.case-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.case-item {
	background-color: #f8f8f8;
	border-radius: 10rpx;
	overflow: hidden;
}

.case-image {
	width: 100%;
	height: 200rpx;
}

.case-title {
	font-size: 28rpx;
	color: #333;
	padding: 15rpx;
	display: block;
	text-align: center;
}
/* 咨询专员模态框样式 */
.contact-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
	position: relative;
	width: 80%;
	background-color: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	z-index: 1001;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1px solid #eee;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-body {
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qrcode-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 30rpx;
}

.qrcode-image {
	width: 300rpx;
	height: 300rpx;
	margin-bottom: 20rpx;
}

.qrcode-text {
	font-size: 28rpx;
	color: #666;
}

.divider {
	width: 100%;
	height: 1px;
	background-color: #eee;
	margin: 20rpx 0;
}

.phone-container {
	display: flex;
	align-items: center;
	padding: 20rpx 40rpx;
	background-color: #f8f8f8;
	border-radius: 50rpx;
	margin: 20rpx 0;
}

.phone-number {
	font-size: 36rpx;
	font-weight: bold;
	color: #0057a3;
	margin-left: 20rpx;
}

.service-time {
	font-size: 24rpx;
	color: #999;
	margin-top: 20rpx;
}
</style>
