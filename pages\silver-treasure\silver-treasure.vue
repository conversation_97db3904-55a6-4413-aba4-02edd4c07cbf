<template>
	<view class="container">
		<web-view :src="webUrl" @message="handleMessage"></web-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			webUrl: 'https://nexthuman.cn/share/#/assembly/?solutionId=sol_23823' // 这里替换为实际需要加载的网页URL
		}
	},
	onLoad() {
		// 可以在这里动态设置要加载的URL
		// 例如从配置或参数中获取
	},
	methods: {
		handleMessage(event) {
			// 处理web-view发送的消息
			console.log('收到web-view消息:', event.detail);
		}
	}
}
</script>

<style>
.container {
	width: 100%;
	height: 100vh;
}
</style>
