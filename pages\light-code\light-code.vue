<template>
	<main-layout>
		<view class="container">
			<!-- 页面标题 -->
			<!-- <view class="page-header">
				<view class="page-title">灵光码</view>
				<view class="page-subtitle">扫描二维码获取老人信息</view>
			</view> -->

			<!-- 用户信息卡片 -->
			<view class="user-card">
				<view class="card-header">
					<view class="card-title">灵光码</view>
					<view class="card-status">已认证</view>
				</view>
				<view class="card-content">
					<view class="card-info-row">
						<view class="card-label">姓名</view>
						<view class="card-value">张明华</view>
					</view>
					<view class="card-info-row">
						<view class="card-label">性别</view>
						<view class="card-value">男</view>
					</view>
					<view class="card-info-row">
						<view class="card-label">年龄</view>
						<view class="card-value">68岁</view>
					</view>
					<view class="card-info-row">
						<view class="card-label">证件号</view>
						<view class="card-value">310********1234</view>
					</view>
				</view>
				<view class="card-footer">
					<view class="qrcode-container">
						<image :src="qrCodeUrl" class="qrcode-image"
						<!-- #ifdef MP-WEIXIN -->
						:show-menu-by-longpress="true"
						<!-- #endif -->
						></image>
					</view>
					<view class="qrcode-text">出示灵光码享受助老服务</view>
					<view class="qrcode-actions">
						<button class="action-button" @click="generateQRCode">重新生成</button>
						<button class="action-button" @click="changeQRCode">切换二维码</button>
					</view>
				</view>
			</view>

			<!-- 使用说明 -->
			<!-- <view class="instructions">
				<view class="instructions-title">使用说明</view>
				<view class="instructions-item">
					<view class="item-number">1</view>
					<view class="item-text">请将此二维码保存至手机相册</view>
				</view>
				<view class="instructions-item">
					<view class="item-number">2</view>
					<view class="item-text">可打印贴于醒目位置，便于扫描</view>
				</view>
				<view class="instructions-item">
					<view class="item-number">3</view>
					<view class="item-text">他人扫描二维码可获取老人基本信息</view>
				</view>
				<view class="instructions-item">
					<view class="item-number">4</view>
					<view class="item-text">紧急情况下可快速联系紧急联系人</view>
				</view>
			</view> -->

			<!-- 紧急联系人 -->
			<view class="emergency-contacts">
				<view class="section-title">紧急联系人</view>
				<view class="contact-item">
					<view class="contact-icon">
						<my-icon type="phone" size="24" color="#BE957E"></my-icon>
					</view>
					<view class="contact-info">
						<view class="contact-name">李小华（子女）</view>
						<view class="contact-phone">138****5678</view>
					</view>
					<view class="contact-action" @click="makePhoneCall('13812345678')">
						<my-icon type="phone" size="24" color="#0057a3"></my-icon>
					</view>
				</view>
				<view class="contact-item">
					<view class="contact-icon">
						<my-icon type="phone" size="24" color="#BE957E"></my-icon>
					</view>
					<view class="contact-info">
						<view class="contact-name">张大明（子女）</view>
						<view class="contact-phone">139****1234</view>
					</view>
					<view class="contact-action" @click="makePhoneCall('13987651234')">
						<my-icon type="phone" size="24" color="#0057a3"></my-icon>
					</view>
				</view>
			</view>
		</view>
	</main-layout>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				name: '张明华',
				gender: '男',
				age: '68岁',
				idCard: '310********1234'
			},
			emergencyContacts: [
				{
					name: '李小华',
					relation: '子女',
					phone: '13812345678'
				},
				{
					name: '张大明',
					relation: '子女',
					phone: '13987651234'
				}
			],
			qrCodeUrl: '/static/images/emptyQrcode.png'
		}
	},
	onLoad() {
		// 这里可以加载用户信息和紧急联系人信息
		// 实际应用中应该从服务器或本地存储获取
		this.generateQRCode();
	},
	methods: {
		makePhoneCall(phoneNumber) {
			uni.makePhoneCall({
				phoneNumber: phoneNumber
			});
		},
		generateQRCode() {
			// 在实际应用中，这里应该调用后端API生成二维码
			// 或者使用前端二维码生成库
			// 这里我们模拟生成过程，使用预先准备的空白二维码

			uni.showLoading({
				title: '生成中...'
			});

			// 模拟生成延迟
			setTimeout(() => {
				// 使用预先准备的空白二维码
				this.qrCodeUrl = '/static/images/emptyQrcode.png';

				uni.hideLoading();
				uni.showToast({
					title: '二维码已生成',
					icon: 'success'
				});
			}, 1000);
		},
		saveQRCode() {
			// 在微信小程序中，可以通过长按二维码保存
			// 这里提供一个额外的保存按钮
			uni.showToast({
				title: '长按二维码可保存',
				icon: 'none',
				duration: 2000
			});

			// 在实际应用中，可以使用以下代码保存图片到相册
			// uni.saveImageToPhotosAlbum({
			//     filePath: this.qrCodeUrl,
			//     success: function() {
			//         uni.showToast({
			//             title: '保存成功',
			//             icon: 'success'
			//         });
			//     },
			//     fail: function() {
			//         uni.showToast({
			//             title: '保存失败',
			//             icon: 'none'
			//         });
			//     }
			// });
		},
		changeQRCode() {
			// 切换二维码，这里可以调用后端API获取新的二维码
			// 或者使用前端二维码生成库生成新的二维码
			// 这里我们模拟切换过程，使用预先准备的空白二维码

			uni.showLoading({
				title: '切换中...'
			});

			// 模拟切换延迟
			setTimeout(() => {
				// 使用预先准备的空白二维码
				this.qrCodeUrl = '/static/images/emptyQrcode.png';
			})
		}
	}
}
</script>

<style>
.container {
	padding: 30rpx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.page-header {
	margin-bottom: 40rpx;
	text-align: center;
}

.page-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.page-subtitle {
	font-size: 28rpx;
	color: #666;
}

.user-card {
	background: linear-gradient(135deg, #BE957E 0%, #A99888 100%);
	border-radius: 20rpx;
	overflow: hidden;
	color: #fff;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
}

.card-status {
	background-color: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
}

.card-content {
	padding: 30rpx;
}

.card-info-row {
	display: flex;
	margin-bottom: 20rpx;
}

.card-label {
	width: 160rpx;
	color: rgba(255, 255, 255, 0.8);
}

.card-value {
	flex: 1;
	font-weight: bold;
}

.card-footer {
	padding: 30rpx;
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
}

.qrcode-container {
	width: 400rpx;
	height: 400rpx;
	background-color: #fff;
	padding: 20rpx;
	border-radius: 10rpx;
	margin-bottom: 20rpx;
}

.qrcode-image {
	width: 100%;
	height: 100%;
}

.qrcode-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	text-align: center;
	margin-bottom: 20rpx;
}

.qrcode-actions {
	display: flex;
	justify-content: space-between;
	width: 100%;
	margin-top: 10rpx;
}

.action-button {
	background-color: rgba(255, 255, 255, 0.2);
	color: #fff;
	border: none;
	border-radius: 30rpx;
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	margin: 0 10rpx;
	flex: 1;
}

.instructions {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.instructions-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.instructions-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.item-number {
	width: 40rpx;
	height: 40rpx;
	background-color: #BE957E;
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	margin-right: 20rpx;
}

.item-text {
	color: #666;
	font-size: 28rpx;
}

.emergency-contacts {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-icon {
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(190, 149, 126, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.contact-info {
	flex: 1;
}

.contact-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 6rpx;
}

.contact-phone {
	font-size: 24rpx;
	color: #666;
}

.contact-action {
	width: 80rpx;
	height: 80rpx;
	background-color: rgba(0, 87, 163, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
