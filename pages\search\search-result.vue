<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="nav-header">
			<!-- <view @click="goBack">
				<uni-icons type="arrow-left" size="24" color="#333"></uni-icons>
			</view> -->
			<view class="search-container">
				<input
					class="search-input"
					type="text"
					v-model="keyword"
					placeholder="请输入搜索关键词"
					confirm-type="search"
					@confirm="search"
				/>
				<view class="search-icon" @click="search">
					<uni-icons type="search" size="20" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 大家搜模块 -->
		<view class="popular-search"><!--  v-if="!hasSearched" -->
			<view class="popular-header">
				<view class="popular-title">大家搜</view>
				<view class="popular-subtitle">常见搜索问题</view>
			</view>
			<view class="popular-tags">
				<view
					class="popular-tag"
					v-for="(tag, index) in popularSearches"
					:key="index"
					@click="searchPopular(tag)"
				>
					{{ tag }}
				</view>
			</view>
		</view>

		<!-- 搜索结果 -->
		<view class="search-result">
			<!-- 加载中 -->
			<view class="loading-container" v-if="loading">
				<view class="loading-spinner"></view>
				<text class="loading-text">搜索中...</text>
			</view>

			<!-- 无结果提示 -->
			<view class="no-result" v-else-if="totalResults === 0">
				<image src="/static/images/no-data.png" class="no-result-image"></image>
				<text class="no-result-text">未找到相关结果</text>
				<text class="no-result-tips">请尝试其他关键词</text>
			</view>

			<!-- 搜索结果展示 -->
			<view class="result-container" v-else>
				<view class="result-summary">
					找到 {{ totalResults }} 条相关结果
				</view>

				<!-- 分类选项卡 -->
				<view class="category-tabs">
					<view
						class="category-tab"
						:class="{ active: activeCategory === 'all' }"
						@click="switchCategory('all')"
					>
						全部
					</view>
					<view
						class="category-tab"
						:class="{ active: activeCategory === 'announcement' }"
						@click="switchCategory('announcement')"
						v-if="announcements.length > 0"
					>
						公告
					</view>
					<view
						class="category-tab"
						:class="{ active: activeCategory === 'policy' }"
						@click="switchCategory('policy')"
						v-if="policies.length > 0"
					>
						政策
					</view>
					<!-- <view
						class="category-tab"
						:class="{ active: activeCategory === 'legal' }"
						@click="switchCategory('legal')"
						v-if="legals.length > 0"
					>
						法律法规
					</view> -->
					<view
						class="category-tab"
						:class="{ active: activeCategory === 'institution' }"
						@click="switchCategory('institution')"
						v-if="institutions.length > 0"
					>
						养老机构
					</view>
				</view>

				<!-- 全部分类 -->
				<view v-if="activeCategory === 'all'">
					<!-- 公告分类 -->
					<view class="result-section" v-if="announcements.length > 0">
						<view class="section-header">
							<view class="section-title">公告</view>
							<view class="section-more" @click="viewMore('announcement')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item"
								v-for="(item, index) in announcements.slice(0, 2)"
								:key="index"
								@click="viewDetail('announcement', item.id)"
							>
								<view class="result-title">{{ item.title }}</view>
								<view class="result-info">
									<text class="result-date">{{ item.date }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 公告分类 -->
				<view v-if="activeCategory === 'announcement' || activeCategory === 'all'">
					<view class="result-section" v-if="announcements.length > 0 && activeCategory === 'announcement'">
						<view class="section-header">
							<view class="section-title">公告</view>
							<view class="section-more" @click="viewMore('announcement')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item"
								v-for="(item, index) in announcements"
								:key="index"
								@click="viewDetail('announcement', item.id)"
							>
								<view class="result-title">{{ item.title }}</view>
								<view class="result-info">
									<text class="result-date">{{ item.date }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 全部分类中的政策部分 -->
				<view v-if="activeCategory === 'all'">
					<!-- 政策分类 -->
					<view class="result-section" v-if="policies.length > 0">
						<view class="section-header">
							<view class="section-title">政策</view>
							<view class="section-more" @click="viewMore('policy')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item"
								v-for="(item, index) in policies.slice(0, 2)"
								:key="index"
								@click="viewDetail('policy', item.id)"
							>
								<view class="result-title">{{ item.title }}</view>
								<view class="result-info">
									<text class="result-source">{{ item.source }}</text>
									<text class="result-date">{{ item.date }}</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 法律法规分类 -->
					<!-- <view class="result-section" v-if="legals.length > 0">
						<view class="section-header">
							<view class="section-title">法律法规</view>
							<view class="section-more" @click="viewMore('legal')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item"
								v-for="(item, index) in legals.slice(0, 2)"
								:key="index"
								@click="viewDetail('legal', item.id)"
							>
								<view class="result-title">{{ item.title }}</view>
								<view class="result-info">
									<text class="result-source">{{ item.source }}</text>
									<text class="result-date">{{ item.date }}</text>
								</view>
							</view>
						</view>
					</view> -->

					<!-- 机构分类 -->
					<view class="result-section" v-if="institutions.length > 0">
						<view class="section-header">
							<view class="section-title">养老机构</view>
							<view class="section-more" @click="viewMore('institution')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item institution-item"
								v-for="(item, index) in institutions.slice(0, 2)"
								:key="index"
								@click="viewDetail('institution', item.id)"
							>
								<image :src="item.image || '/static/images/institution-default.png'" mode="aspectFill" class="institution-image"></image>
								<view class="institution-info">
									<view class="result-title">{{ item.name }}</view>
									<view class="result-info">
										<text class="result-address">{{ item.address }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 政策分类详情 -->
				<view v-if="activeCategory === 'policy'">
					<view class="result-section" v-if="policies.length > 0">
						<view class="section-header">
							<view class="section-title">政策</view>
							<view class="section-more" @click="viewMore('policy')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item"
								v-for="(item, index) in policies"
								:key="index"
								@click="viewDetail('policy', item.id)"
							>
								<view class="result-title">{{ item.title }}</view>
								<view class="result-info">
									<text class="result-source">{{ item.source }}</text>
									<text class="result-date">{{ item.date }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 法律法规分类详情 -->
				<view v-if="activeCategory === 'legal'">
					<view class="result-section" v-if="legals.length > 0">
						<view class="section-header">
							<view class="section-title">法律法规</view>
							<view class="section-more" @click="viewMore('legal')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item"
								v-for="(item, index) in legals"
								:key="index"
								@click="viewDetail('legal', item.id)"
							>
								<view class="result-title">{{ item.title }}</view>
								<view class="result-info">
									<text class="result-source">{{ item.source }}</text>
									<text class="result-date">{{ item.date }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 机构分类详情 -->
				<view v-if="activeCategory === 'institution'">
					<view class="result-section" v-if="institutions.length > 0">
						<view class="section-header">
							<view class="section-title">机构</view>
							<view class="section-more" @click="viewMore('institution')">
								查看更多
								<uni-icons type="right" size="14" color="#999"></uni-icons>
							</view>
						</view>
						<view class="result-list">
							<view
								class="result-item institution-item"
								v-for="(item, index) in institutions"
								:key="index"
								@click="viewDetail('institution', item.id)"
							>
								<image :src="item.image || '/static/images/institution-default.png'" mode="aspectFill" class="institution-image"></image>
								<view class="institution-info">
									<view class="result-title">{{ item.name }}</view>
									<view class="result-info">
										<text class="result-address">{{ item.address }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			keyword: '',
			loading: false,
			totalResults: 0,
			hasSearched: false, // 是否已经搜索过
			firstSearched: true, // 是否是第一次搜索
			activeCategory: 'all', // 当前选中的分类
			announcements: [],
			policies: [],
			legals: [],
			institutions: [],
			// 热门搜索标签
			popularSearches: [
				'养老补贴',
				'医保政策',
				'居家养老',
				'养老院',
				'长期护理保险',
				'老年人体检',
				'社区食堂',
				'助餐服务',
				'康复护理',
				'老年大学',
				'敬老卡',
				'高龄津贴'
			]
		}
	},
	onLoad(options) {
		if (options.keyword) {
			this.keyword = decodeURIComponent(options.keyword);
			this.search();
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		search() {
			if (!this.keyword.trim()) {
				uni.showToast({
					title: '请输入搜索关键词',
					icon: 'none'
				});
				return;
			}

			this.loading = true;
			this.hasSearched = true; // 标记已经搜索过
			//this.firstSearched = false; // 标记是否是第一次搜索

			uni.request({
				url: this.$backUrl + '/client/c/search/all?keyword=' + this.keyword,
				method: 'GET',
				success: (res) => {
					console.log(res);
					this.announcements = res.data.data.for
				}
			});

			// 模拟搜索请求
			setTimeout(() => {
				// 模拟搜索结果
				this.announcements = this.searchAnnouncements(this.keyword);
				this.policies = this.searchPolicies(this.keyword);
				this.legals = this.searchLegals(this.keyword);
				this.institutions = this.searchInstitutions(this.keyword);

				// 计算总结果数
				this.totalResults = this.announcements.length + this.policies.length + this.legals.length + this.institutions.length;

				this.loading = false;
			}, 1000);
		},
		// 点击热门搜索标签
		searchPopular(tag) {
			this.keyword = tag;
			this.search();
		},
		// 模拟搜索公告
		searchAnnouncements(keyword) {
			const announcements = [
				{ id: 1, title: '关于开展2023年老年人健康体检的通知', date: '2023-05-15' },
				{ id: 2, title: '宝山区养老服务补贴申请指南', date: '2023-04-20' },
				{ id: 3, title: '关于举办老年人智能手机使用培训班的通知', date: '2023-06-01' }
			];

			return announcements.filter(item => item.title.includes(keyword));
		},
		// 模拟搜索政策
		searchPolicies(keyword) {
			const policies = [
				{ id: 1, title: '宝山区居家养老服务补贴实施办法', source: '宝山区民政局', date: '2023-03-10' },
				{ id: 2, title: '关于加强老年人长期护理保险服务的实施意见', source: '宝山区医保局', date: '2023-02-15' },
				{ id: 3, title: '宝山区养老机构管理规范', source: '宝山区民政局', date: '2022-12-20' }
			];

			return policies.filter(item => item.title.includes(keyword) || item.source.includes(keyword));
		},
		// 模拟搜索法律法规
		searchLegals(keyword) {
			const legals = [
				{ id: 1, title: '中华人民共和国老年人权益保障法', source: '全国人民代表大会常务委员会', date: '2018-12-29' },
				{ id: 2, title: '上海市养老服务条例', source: '上海市人民代表大会常务委员会', date: '2021-05-20' },
				{ id: 3, title: '上海市老年人权益保障条例', source: '上海市人民代表大会常务委员会', date: '2019-07-18' }
			];

			return legals.filter(item => item.title.includes(keyword) || item.source.includes(keyword));
		},
		// 模拟搜索机构
		searchInstitutions(keyword) {
			const institutions = [
				{ id: 1, name: '宝山区阳光老年公寓', address: '宝山区友谊路123号', image: '/static/images/institution/institution1.png' },
				{ id: 2, name: '宝山区康乐养老院', address: '宝山区牡丹江路456号', image: '/static/images/institution/institution2.png' },
				{ id: 3, name: '宝山区颐养天年养老中心', address: '宝山区淞宝路789号', image: '/static/images/institution/institution3.png' }
			];

			return institutions.filter(item => item.name.includes(keyword) || item.address.includes(keyword));
		},
		// 切换分类
		switchCategory(category) {
			this.activeCategory = category;
		},
		// 查看更多结果
		viewMore(type) {
			switch (type) {
				case 'announcement':
					// 跳转到公告列表页
					uni.showToast({
						title: '查看更多公告',
						icon: 'none'
					});
					break;
				case 'policy':
					// 跳转到政策列表页
					uni.navigateTo({
						url: '/pages/policy/policy'
					});
					break;
				case 'legal':
					// 跳转到法律法规列表页
					uni.navigateTo({
						url: '/pages/legal/legal'
					});
					break;
				case 'institution':
					// 跳转到机构列表页
					uni.navigateTo({
						url: '/pages/yljg/yljg'
					});
					break;
			}
		},
		// 查看详情
		viewDetail(type, id) {
			switch (type) {
				case 'announcement':
					// 跳转到公告详情页
					uni.showToast({
						title: '查看公告详情: ' + id,
						icon: 'none'
					});
					break;
				case 'policy':
					// 跳转到政策详情页
					uni.navigateTo({
						url: `/pages/policy/policy-detail?id=${id}`
					});
					break;
				case 'legal':
					// 跳转到法律法规详情页
					uni.navigateTo({
						url: `/pages/legal/legal-detail?id=${id}`
					});
					break;
				case 'institution':
					// 跳转到机构详情页
					uni.navigateTo({
						url: `/pages/institution-detail/institution-detail?id=${id}`
					});
					break;
			}
		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.nav-header {
	display: flex;
	align-items: center;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.search-container {
	flex: 1;
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 20px;
	padding: 0 15px;
	margin-left: 10px;
	height: 40px;
}

.search-input {
	flex: 1;
	height: 40px;
	font-size: 14px;
}

.search-icon {
	padding: 5px;
}

.search-result {
	padding: 15px;
}

/* 加载中样式 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.loading-spinner {
	width: 40px;
	height: 40px;
	border: 3px solid #f3f3f3;
	border-top: 3px solid #c8a287;
	border-radius: 50%;
	/* 使用小程序支持的动画方式 */
	animation-name: spin;
	animation-duration: 1s;
	animation-timing-function: linear;
	animation-iteration-count: infinite;
	margin-bottom: 10px;
}

/* 小程序支持的关键帧动画 */
@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.loading-text {
	font-size: 14px;
	color: #999;
}

/* 无结果样式 */
.no-result {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.no-result-image {
	width: 100px;
	height: 100px;
	margin-bottom: 15px;
}

.no-result-text {
	font-size: 16px;
	color: #333;
	margin-bottom: 10px;
}

.no-result-tips {
	font-size: 14px;
	color: #999;
}

/* 结果容器样式 */
.result-container {
	padding-bottom: 20px;
}

.result-summary {
	font-size: 14px;
	color: #666;
	margin-bottom: 15px;
}

.result-section {
	background-color: white;
	border-radius: 8px;
	margin-bottom: 15px;
	overflow: hidden;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 12px;
	color: #999;
	display: flex;
	align-items: center;
}

.result-list {
	padding: 0 15px;
}

.result-item {
	padding: 15px 0;
	border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
	border-bottom: none;
}

.result-title {
	font-size: 15px;
	color: #333;
	margin-bottom: 5px;
}

.result-info {
	display: flex;
	font-size: 12px;
	color: #999;
}

.result-source {
	margin-right: 10px;
}

/* 机构项目样式 */
.institution-item {
	display: flex;
	align-items: center;
}

.institution-image {
	width: 60px;
	height: 60px;
	border-radius: 5px;
	margin-right: 10px;
}

.institution-info {
	flex: 1;
}

/* 分类选项卡样式 */
.category-tabs {
	display: flex;
	overflow-x: auto;
	background-color: white;
	padding: 0 10px;
	margin-bottom: 15px;
	border-radius: 8px;
	white-space: nowrap;
}

.category-tab {
	padding: 12px 15px;
	font-size: 14px;
	color: #666;
	position: relative;
	flex-shrink: 0;
}

.category-tab.active {
	color: #c8a287;
	font-weight: bold;
}

.category-tab.active::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 15px;
	right: 15px;
	height: 3px;
	background-color: #c8a287;
	border-radius: 3px;
}

/* 大家搜模块样式 */
.popular-search {
	background-color: white;
	margin: 15px;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.popular-header {
	margin-bottom: 15px;
}

.popular-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
}

.popular-subtitle {
	font-size: 14px;
	color: #999;
}

.popular-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.popular-tag {
	background-color: #f8f8f8;
	color: #666;
	padding: 8px 15px;
	border-radius: 20px;
	font-size: 14px;
	border: 1px solid #e8e8e8;
	transition: all 0.3s ease;
}

.popular-tag:active {
	background-color: #c8a287;
	color: white;
	border-color: #c8a287;
}
</style>
