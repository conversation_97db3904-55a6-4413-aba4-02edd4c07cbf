<template>
  <view class="custom-tabbar">
    <image class="tabbar-bg" src="https://kodo.dingsd115.com/bs-uniapp/tabBack.png" mode="aspectFill"></image>
    <view class="tabbar-overlay"></view>
    <view class="tabbar-content">
      <view
        v-for="(item, index) in tabList"
        :key="index"
        class="tabbar-item"
        :class="{ 'active': currentPage === item.pagePath, 'center-item': index === 2 || index === 4 }"
        @click="switchTab(item.pagePath)"
      >
        <view class="tabbar-icon-container" :class="{ 'center-icon-container': index === 2, 'right-icon-container': index === 4}">
          <image
            :src="currentPage === item.pagePath ? item.selectedIconPath : item.iconPath"
            class="tabbar-icon"
            :class="{ 'center-icon': index === 2, 'right-icon': index === 4 }"
          ></image>
        </view>
        <text class="tabbar-text" :class="{ 'active-text': currentPage === item.pagePath }">{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomTabbar',
  data() {
    return {
      currentPage: '/pages/index/index',
      tabList: [
        {
          pagePath: '/pages/index/index',
          text: '首页',
          iconPath: '/static/images/tabbar/tab1.png',
          selectedIconPath: '/static/images/tabbar/tab1.png'
        },
        {
          pagePath: '/pages/all-services/all-services',
          text: '服务',
          iconPath: '/static/images/tabbar/tab2.png',
          selectedIconPath: '/static/images/tabbar/tab2.png'
        },
        {
          pagePath: '/pages/index/index1',
          text: '急救',
          iconPath: '/static/images/tabbar/tab3.png',
          selectedIconPath: '/static/images/tabbar/tab3.png'
        },
        {
          pagePath: '/pages/light-code/light-code',
          text: '灵光码',
          iconPath: '/static/images/tabbar/tab4.png',
          selectedIconPath: '/static/images/tabbar/tab4.png'
        },
        {
          pagePath: '/pages/silver-treasure/silver-treasure',
          text: '银灵宝',
          iconPath: '/static/images/tabbar/tab6.png',
          selectedIconPath: '/static/images/tabbar/tab6.png'
        }
      ]
    };
  },
  created() {
    // 获取当前页面路径
    const pages = getCurrentPages();
    if (pages.length) {
      const currentPage = pages[pages.length - 1];
      this.currentPage = '/' + currentPage.route;
    }
  },
  methods: {
    switchTab(url) {
      if (this.currentPage === url) return;

	  if(url.indexOf('index1') > 0 || url.indexOf('light-code') > 0) {
		  uni.showToast({
		  	title: '服务建设中',
		  	icon: 'none',
		  	duration: 2000
		  });
		  return;
	  }

      // 更新当前页面
      this.currentPage = url;

      // 跳转到对应页面
      uni.redirectTo({
        url: url
      });
    }
  }
};
</script>

<style>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 180rpx;
  z-index: 999;
}

.tabbar-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
}

.tabbar-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
  /* background-color: rgba(80, 70, 60, 0.9); */
}

.tabbar-content {
  position: relative;
  display: flex;
  height: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  /* padding-top: 20rpx; */
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  padding-top: 0;
}

.tabbar-icon-container {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-icon-container {
  width: 130rpx;
  height: 130rpx;
  background-color: #C8A287;
  border-radius: 50%;
  margin-top: 0;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.right-icon-container {
	width: 130rpx;
	height: 130rpx;
	border-radius: 50%;
	margin-top: 0;
	/* box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1); */
}

.tabbar-icon {
  width: 80rpx;
  height: 80rpx;
}

.center-icon {
  width: 95rpx;
  height: 95rpx;
}

.right-icon {
  width: 120rpx;
  height: 120rpx;
}

.tabbar-text {
  font-size: 32rpx;
  color: #e0d0c0;
  margin-top: 15rpx;
  font-weight: 500;
}

.active-text {
  color: #ffffff;
  font-weight: bold;
}

.center-item {
  transform: translateY(-22rpx);
}
</style>
