<template>
	<view class="container">

		<!-- 用户信息头部 -->
		<view class="user-header">
			<view class="user-info" @click="doLogin">
				<view class="avatar-container">
					<image :src="userInfo.avatar || '/static/images/avatar/avatar.svg'" class="avatar"></image>
				</view>
				<view class="user-details">
					<view class="user-name">{{ userInfo.nickname || '微信用户' }}</view>
					<view class="user-id">ID: {{ userInfo.id }}</view>
				</view>
			</view>
			<!-- <view class="header-actions">
				<view class="action-item" @click="navigateTo('/pages/settings/settings')">
					<uni-icons type="gear" size="20"></uni-icons>
				</view>
				<view class="action-item" @click="navigateTo('/pages/notifications/notifications')">
					<uni-icons type="chat" size="24"></uni-icons>
					<view class="notification-badge" v-if="notificationCount > 0">{{notificationCount}}</view>
				</view>
			</view> -->
		</view>

		<!-- 用户卡片信息 -->
		<!-- <view class="user-card">
			<view class="card-header">
				<view class="card-title">老年人优待证</view>
				<view class="card-status">已认证</view>
			</view>
			<view class="card-content">
				<view class="card-info-row">
					<view class="card-label">姓名</view>
					<view class="card-value">张明华</view>
				</view>
				<view class="card-info-row">
					<view class="card-label">性别</view>
					<view class="card-value">男</view>
				</view>
				<view class="card-info-row">
					<view class="card-label">年龄</view>
					<view class="card-value">68岁</view>
				</view>
				<view class="card-info-row">
					<view class="card-label">证件号</view>
					<view class="card-value">310********1234</view>
				</view>
			</view>
			<view class="card-footer">
				<view class="card-qr">
					<image src="/static/images/qr-code.png" class="qr-image"></image>
				</view>
				<view class="card-note">出示二维码享受各项优惠服务</view>
			</view>
		</view> -->

		<!-- 功能菜单 -->
		<!-- <view class="menu-section">
			<view class="menu-title">我的服务</view>
			<view class="menu-grid">
				<view class="menu-item" v-for="(item, index) in serviceMenu" :key="index" @click="navigateTo(item.url)">
					<view class="menu-icon" :style="{backgroundColor: item.bgColor}">
						<text class="iconfont" :class="item.icon"></text>
					</view>
					<view class="menu-name">{{item.name}}</view>
				</view>
			</view>
		</view> -->

		<!-- 列表菜单 -->
		<view class="list-menu">
			<view class="list-item" v-for="(item, index) in listMenu" :key="index" @click="navigateTo(item.url)">
				<view class="list-item-left">
					<uni-icons :type="item.icon" size="24"></uni-icons>
					<text class="list-item-text">{{item.name}}</text>
				</view>
				<uni-icons type="right" size="20"></uni-icons>
			</view>
		</view>

		<!-- 底部信息 -->
		<view class="footer-info">
			<view class="footer-text">宝山区为老服务平台</view>
			<view class="footer-text">0.0.1</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			notificationCount: 0,
			userInfo: {
				nickname: '微信用户',
				id: 'N/A'
			},
			serviceMenu: [
				{
					name: '我的预约',
					icon: 'icon-calendar-check',
					url: '/pages/my-appointments/my-appointments',
					bgColor: '#f0d6c2'
				},
				{
					name: '我的订单',
					icon: 'icon-file-invoice',
					url: '/pages/my-orders/my-orders',
					bgColor: '#d6e5f0'
				},
				{
					name: '健康档案',
					icon: 'icon-heartbeat',
					url: '/pages/health-records/health-records',
					bgColor: '#d6f0d8'
				},
				{
					name: '我的收藏',
					icon: 'icon-star',
					url: '/pages/my-favorites/my-favorites',
					bgColor: '#f0e5d6'
				}
			],
			listMenu: [
				{
					name: '个人信息',
					icon: 'person',
					url: '/pages/profile/profile'
				},
				/* {
					name: '地址管理',
					icon: 'paperplane',
					url: '/pages/address/address'
				}, */
				{
					name: '老人信息',
					icon: 'staff',
					url: '/pages/old-people/old-people'
				},
				{
					name: '紧急联系人',
					icon: 'phone',
					url: '/pages/emergency-contacts/emergency-contacts'
				}
				/* {
					name: '意见反馈',
					icon: 'email',
					url: '/pages/feedback/feedback'
				},
				{
					name: '关于我们',
					icon: 'info',
					url: '/pages/about/about'
				} */
			]
		}
	},
	onLoad() {
		if(this.checkLogin()) {
			this.getUserInfo();
		}
	},
	methods: {
		updateTime() {
			const now = new Date();
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}`;
		},
		navigateTo(url) {
			if(this.checkLogin()) {
				uni.navigateTo({
					url: url
				});
				return;
			}
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});
		},
		doLogin() {
			let _this = this;
			if (!_this.checkLogin()) {
				uni.showLoading({
					title: '登录中...'
				});
				uni.login({
					provider: 'weixin', //使用微信登录
					success: function (res) {
						const code = res.code;
						if(!code) {
							uni.showToast({
								title: '获取用户登录凭证失败',
								icon: 'none'
							});
							return;
						}
						// 使用code换取token
						_this.getToken(code);
					}
				});
			} else {
				if(!(_this.userInfo.nickname === '' || this.userInfo.nickname === undefined || _this.userInfo.nickname === '微信用户' ||
					_this.userInfo.nickname === null)
				) {
					uni.navigateTo({
						url: '/pages/profile/profile'
					});
				}
			}
		},
		getToken(code) {
			if(this.checkLogin()) {
				return uni.getStorageSync('token')
			}
			uni.request({
				url: this.$backUrl + '/auth/third/callback-client',
				method: 'GET',
				data: {
					platform: 'WECHAT_MINI',
					code: code,
					state: code
				},
				header: {
					'content-type': 'application/x-www-form-urlencoded'
				},
				success: (res) => {
					uni.setStorageSync('token', res.data.data);
					this.getUserInfo();
				}
			})
		},
		checkLogin() {
			const token = uni.getStorageSync('token');
			if (!token) {
				return false;
			} else {
				return true;
			}
		},
		getUserInfo() {
			uni.request({
				url: this.$backUrl + '/auth/c/getLoginUser',
				method: 'GET',
				header: {
					'token': this.getToken()
				},
				success: (res) => {
					this.userInfo = {...res.data.data};
					uni.hideLoading();
					if(this.userInfo.nickname === '' || this.userInfo.nickname === undefined || this.userInfo.nickname === '微信用户' ||
						this.userInfo.nickname === null) {
						uni.navigateTo({
							url: '/pages/profile/profile'
						});
					}
					uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));
				}
			});
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 30px;
	/* background-color: #f5f5f5; */
}

.ml-2 {
	margin-left: 8px;
}

/* 用户页面特定样式 */
.user-header {
	background-color: #c8a287;
	padding: 20px 15px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.user-info {
	display: flex;
	align-items: center;
}

.avatar-container {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	overflow: hidden;
	border: 3px solid rgba(255, 255, 255, 0.3);
	margin-right: 15px;
}

.avatar {
	width: 100%;
	height: 100%;
}

.user-details {
	color: white;
}

.user-name {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 5px;
}

.user-id {
	font-size: 14px;
	opacity: 0.8;
}

.header-actions {
	display: flex;
}

.action-item {
	width: 40px;
	height: 40px;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 10px;
	color: white;
	position: relative;
}

.notification-badge {
	position: absolute;
	top: -5px;
	right: -5px;
	background-color: #ff4d4f;
	color: white;
	font-size: 12px;
	min-width: 18px;
	height: 18px;
	border-radius: 9px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.user-card {
	margin: 15px;
	background-color: white;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
	background-color: #c8a287;
	padding: 15px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: white;
}

.card-title {
	font-size: 16px;
	font-weight: bold;
}

.card-status {
	background-color: rgba(255, 255, 255, 0.2);
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
}

.card-content {
	padding: 15px;
}

.card-info-row {
	display: flex;
	margin-bottom: 10px;
}

.card-label {
	width: 80px;
	color: #666;
}

.card-value {
	flex: 1;
	font-weight: bold;
}

.card-footer {
	padding: 15px;
	border-top: 1px solid #f0f0f0;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.card-qr {
	width: 120px;
	height: 120px;
	margin-bottom: 10px;
}

.qr-image {
	width: 100%;
	height: 100%;
}

.card-note {
	color: #999;
	font-size: 12px;
}

.menu-section {
	margin: 15px;
}

.menu-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
}

.menu-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 15px;
}

.menu-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.menu-icon {
	width: 50px;
	height: 50px;
	border-radius: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
	color: #c8a287;
}

.menu-name {
	font-size: 12px;
	color: #333;
}

.list-menu {
	margin: 15px;
	background-color: white;
	border-radius: 10px;
	overflow: hidden;
}

.list-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.list-item:last-child {
	border-bottom: none;
}

.list-item-left {
	display: flex;
	align-items: center;
}

.list-item-text {
	margin-left: 10px;
}

.footer-info {
	margin-top: 30px;
	text-align: center;
	color: #999;
	font-size: 12px;
}

.footer-text {
	margin-bottom: 5px;
}

/* 临时图标样式，实际应使用iconfont */
.iconfont {
	font-family: "iconfont";
}

.icon-signal:before {
	content: "\e8d7";
}

.icon-wifi:before {
	content: "\e8d8";
}

.icon-battery-full:before {
	content: "\e8d9";
}

.icon-cog:before {
	content: "\e8fb";
}

.icon-bell:before {
	content: "\e8fc";
}

.icon-calendar-check:before {
	content: "\e8f7";
}

.icon-file-invoice:before {
	content: "\e8fd";
}

.icon-heartbeat:before {
	content: "\e8fe";
}

.icon-star:before {
	content: "\e8f6";
}

.icon-user:before {
	content: "\e8ff";
}

.icon-map-marker-alt:before {
	content: "\e900";
}

.icon-phone-alt:before {
	content: "\e901";
}

.icon-comment-alt:before {
	content: "\e902";
}

.icon-info-circle:before {
	content: "\e903";
}

.icon-chevron-right:before {
	content: "\e904";
}
</style>
