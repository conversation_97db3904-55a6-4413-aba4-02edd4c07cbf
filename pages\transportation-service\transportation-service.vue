<template>
	<view class="container">
		<!-- 状态栏 -->
		<!-- <view class="status-bar">
			<view>{{currentTime}}</view>
			<view>
				<text class="iconfont icon-signal"></text>
				<text class="iconfont icon-wifi ml-2"></text>
				<text class="iconfont icon-battery-full ml-2"></text>
			</view>
		</view> -->
		
		<!-- 导航头部 -->
		<!-- <view class="nav-header">
			<view @click="goBack">
				<text class="iconfont icon-arrow-left"></text>
			</view>
			<view class="page-title">出行服务</view>
			<view>
				<text class="iconfont icon-ellipsis-h"></text>
			</view>
		</view> -->
		
		<!-- Banner -->
		<view class="banner">
			<image src="https://kodo.dingsd115.com/bs-uniapp/banner-cxfw.png" alt="老年人专享出行服务" class="w-full h-full"></image>
			<!-- <view class="banner-overlay">
				<view class="banner-title">老年人专享出行服务</view>
				<view class="banner-subtitle">安全、便捷、舒适的出行体验</view>
			</view> -->
		</view>
		
		<!-- 服务标签 -->
		<scroll-view scroll-x="true" class="service-tabs">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index" 
				:class="['service-tab', activeTab === index ? 'active' : '']"
				@click="changeTab(index)"
			>
				{{tab.name}}
			</view>
		</scroll-view>
		
		<!-- 出行服务列表 -->
		<view class="service-list">
			<view class="service-item" v-for="(service, index) in services" :key="index">
				<view class="service-image">
					<image :src="service.image" :alt="service.name" class="w-full h-full"></image>
				</view>
				<view class="service-info">
					<view class="service-header">
						<view class="service-name">{{service.name}}</view>
						<view class="service-price">{{service.price}}</view>
					</view>
					<view class="service-description">{{service.description}}</view>
					<view class="service-features">
						<view class="feature-item" v-for="(feature, featureIndex) in service.features" :key="featureIndex">
							<text class="iconfont icon-check mr-1"></text>
							<text>{{feature}}</text>
						</view>
					</view>
					<view class="service-actions">
						<button class="action-button primary" @click="bookService(service)">立即预约</button>
						<button class="action-button secondary" @click="callService(service)">电话咨询</button>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 常见问题 -->
		<view class="faq-section">
			<view class="section-title">常见问题</view>
			<view class="faq-item" v-for="(faq, index) in faqs" :key="index" @click="toggleFaq(index)">
				<view class="faq-question">
					<text>{{faq.question}}</text>
					<text :class="['iconfont', faq.expanded ? 'icon-chevron-up' : 'icon-chevron-down']"></text>
				</view>
				<view class="faq-answer" v-if="faq.expanded">
					{{faq.answer}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentTime: '14:05',
			activeTab: 0,
			tabs: [
				{ name: '全部' },
				{ name: '专车接送' },
				{ name: '公交出行' },
				{ name: '医院接送' },
				{ name: '社区巴士' }
			],
			services: [
				{
					name: '老年人专车接送服务',
					image: 'https://images.unsplash.com/photo-1590362891991-f776e747a588?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
					price: '¥20起/次',
					description: '为老年人提供专业、安全、舒适的专车接送服务，可提前预约，灵活安排出行时间。',
					features: ['专业驾驶员', '车辆消毒', '轮椅无障碍', '上门接送'],
					type: '专车接送'
				},
				{
					name: '医院就医接送服务',
					image: 'https://images.unsplash.com/photo-1516841273335-e39b37888115?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
					price: '¥30起/次',
					description: '提供往返医院的专车接送服务，包括等候、陪诊和接送回家等全流程服务。',
					features: ['预约挂号', '全程陪同', '轮椅服务', '代取药品'],
					type: '医院接送'
				}
			],
			faqs: [
				{
					question: '如何预约出行服务？',
					answer: '您可以通过本应用直接预约，或者拨打服务热线************进行电话预约。预约时请提前至少2小时，以便我们合理安排车辆和人员。',
					expanded: false
				},
				{
					question: '出行服务的收费标准是什么？',
					answer: '专车接送服务起步价20元，包含5公里以内的行程，超出部分按照每公里3元计费。医院接送服务起步价30元，包含往返接送和1小时等候时间，超出等候时间按照每小时10元计费。',
					expanded: false
				},
				{
					question: '是否支持轮椅和行动不便老人乘坐？',
					answer: '是的，我们的专车都配备了轮椅升降设备和专业的护理人员，可以安全、舒适地接送行动不便的老年人。',
					expanded: false
				}
			]
		}
	},
	onLoad() {
		this.updateTime();
		// 设置定时器，每分钟更新一次时间
		setInterval(this.updateTime, 60000);
	},
	methods: {
		updateTime() {
			const now = new Date();
			const hours = now.getHours().toString().padStart(2, '0');
			const minutes = now.getMinutes().toString().padStart(2, '0');
			this.currentTime = `${hours}:${minutes}`;
		},
		goBack() {
			uni.navigateBack();
		},
		changeTab(index) {
			this.activeTab = index;
			
			// 根据标签筛选服务
			// 这里简化处理，实际应用中应该根据筛选条件从服务器获取数据
			const tabType = this.tabs[index].name;
			if (tabType === '全部') {
				// 显示所有服务
			} else {
				// 根据类型筛选服务
				// 这里只是示例，实际应用中需要根据真实数据处理
			}
		},
		bookService(service) {
			uni.navigateTo({
				url: '/pages/booking/booking?type=transportation&service=' + encodeURIComponent(JSON.stringify(service))
			});
		},
		callService(service) {
			uni.makePhoneCall({
				phoneNumber: '************'
			});
		},
		toggleFaq(index) {
			this.faqs[index].expanded = !this.faqs[index].expanded;
		}
	}
}
</script>

<style>
.container {
	padding-bottom: 20px;
}

.ml-2 {
	margin-left: 8px;
}

.mr-1 {
	margin-right: 4px;
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100%;
}

/* 出行服务页面特定样式 */
.nav-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 15px;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

.banner {
	height: 80px;
	position: relative;
	overflow: hidden;
	margin-bottom: 20px;
}

.banner-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
	padding: 20px 15px;
}

.banner-title {
	color: white;
	font-size: 22px;
	font-weight: bold;
	margin-bottom: 5px;
}

.banner-subtitle {
	color: rgba(255,255,255,0.8);
	font-size: 14px;
}

.service-tabs {
	white-space: nowrap;
	padding: 0 15px;
	margin-bottom: 20px;
}

.service-tab {
	display: inline-block;
	padding: 8px 15px;
	margin-right: 10px;
	background-color: #f5f5f5;
	border-radius: 20px;
	font-size: 14px;
}

.service-tab.active {
	background-color: #c8a287;
	color: white;
}

.service-list {
	padding: 0 15px;
}

.service-item {
	background-color: white;
	border-radius: 10px;
	margin-bottom: 15px;
	overflow: hidden;
	box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.service-image {
	height: 150px;
	overflow: hidden;
}

.service-info {
	padding: 15px;
}

.service-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.service-name {
	font-weight: bold;
	font-size: 16px;
}

.service-price {
	color: #c8a287;
	font-weight: bold;
}

.service-description {
	color: #666;
	font-size: 14px;
	margin-bottom: 15px;
	line-height: 1.5;
}

.service-features {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 15px;
}

.feature-item {
	width: 50%;
	display: flex;
	align-items: center;
	color: #666;
	font-size: 14px;
	margin-bottom: 8px;
}

.service-actions {
	display: flex;
	gap: 10px;
}

.action-button {
	flex: 1;
	padding: 10px;
	border-radius: 5px;
	font-size: 14px;
	text-align: center;
}

.action-button.primary {
	background-color: #c8a287;
	color: white;
}

.action-button.secondary {
	background-color: #f5f5f5;
	color: #333;
}

.faq-section {
	padding: 0 15px;
	margin-top: 30px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 15px;
}

.faq-item {
	background-color: white;
	border-radius: 10px;
	padding: 15px;
	margin-bottom: 10px;
	box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.faq-question {
	display: flex;
	justify-content: space-between;
	font-weight: bold;
}

.faq-answer {
	margin-top: 10px;
	color: #666;
	font-size: 14px;
	line-height: 1.5;
}

/* 临时图标样式，实际应使用iconfont */
.iconfont {
	font-family: "iconfont";
}

.icon-signal:before {
	content: "\e8d7";
}

.icon-wifi:before {
	content: "\e8d8";
}

.icon-battery-full:before {
	content: "\e8d9";
}

.icon-arrow-left:before {
	content: "\e8f4";
}

.icon-ellipsis-h:before {
	content: "\e8f5";
}

.icon-check:before {
	content: "\e8f8";
}

.icon-chevron-down:before {
	content: "\e8f9";
}

.icon-chevron-up:before {
	content: "\e8fa";
}
</style>
