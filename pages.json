{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path": "pages/activity/activity-list",
			"style": {
				"navigationBarTitleText": "精彩活动"
			}
		},
		{
			"path": "pages/activity/activity-detail",
			"style": {
				"navigationBarTitleText": "活动详情"
			}
		},
		{
			"path": "pages/all-services/all-services",
			"style": {
				"navigationBarTitleText": "所有服务"
			}
		},
		{
			"path": "pages/digital-map/digital-map",
			"style": {
				"navigationBarTitleText": "数字地图"
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationBarTitleText": "我的"
			}
		},
		{
			"path": "pages/profile/profile",
			"style": {
				"navigationBarTitleText": "个人信息"
			}
		},
		{
			"path": "pages/policy/policy",
			"style": {
				"navigationBarTitleText": "养老政策"
			}
		},
		{
			"path": "pages/policy/policy-detail",
			"style": {
				"navigationBarTitleText": "政策详情"
			}
		},
		{
			"path": "pages/meal-service/meal-service",
			"style": {
				"navigationBarTitleText": "用餐服务"
			}
		},
		{
			"path": "pages/medical-service/medical-service",
			"style": {
				"navigationBarTitleText": "就医服务"
			}
		},
		{
			"path" : "pages/yljg/yljg",
			"style" :
			{
				"navigationBarTitleText" : "养老机构"
			}
		},
		{
			"path" : "pages/bsjg/bsjg",
			"style" :
			{
				"navigationBarTitleText" : "办事机构"
			}
		},
		{
			"path" : "pages/institution-detail/institution-detail",
			"style" :
			{
				"navigationBarTitleText" : "机构详情",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/legal/legal",
			"style" :
			{
				"navigationBarTitleText" : "法律法规",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/legal/legal-detail",
			"style" :
			{
				"navigationBarTitleText" : "法律法规详情",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/model-room/model-room",
			"style" :
			{
				"navigationBarTitleText" : "适老样板间",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
				// "navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/light-code/light-code",
			"style" :
			{
				"navigationBarTitleText" : "灵光码",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/silver-treasure/silver-treasure",
			"style" :
			{
				"navigationBarTitleText" : "银灵宝",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/elderly-news/news-list",
			"style" :
			{
				"navigationBarTitleText" : "养老资讯",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/elderly-news/news-detail",
			"style" :
			{
				"navigationBarTitleText" : "资讯详情",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/old-people/old-people",
			"style" :
			{
				"navigationBarTitleText" : "老人信息",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/old-people/elderly-detail",
			"style" :
			{
				"navigationBarTitleText" : "老人详情",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/search/search-result",
			"style" :
			{
				"navigationBarTitleText" : "搜索结果",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/emergency-contacts/emergency-contacts",
			"style" :
			{
				"navigationBarTitleText" : "紧急联系人",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		},
		{
			"path" : "pages/emergency-contacts/contact-edit",
			"style" :
			{
				"navigationBarTitleText" : "紧急联系人详情",
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTextStyle": "black"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {},
	// #ifdef MP-WEIXIN
	"plugins": {
	    "WechatSI": {
	      "version": "0.3.6",
	      "provider": "wx069ba97219f66d99"
		}
	}
	// #endif
}
