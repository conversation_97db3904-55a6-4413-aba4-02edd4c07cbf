<template>
	<view class="container">

		<!-- 个人信息列表 -->
		<view class="profile-list">
			<!-- 头像 -->
			<view class="profile-item">
				<view class="item-label">头像</view>
				<view class="item-content">
					<button plain="true" type="default" style="border: none;" class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
						<image :src="userInfo.avatar || '/static/images/avatar/avatar.svg'" class="avatar-image"></image>
					</button>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 昵称 -->
			<view class="profile-item" @click="editNickname">
				<view class="item-label">昵称</view>
				<view class="item-content">
					<text class="item-value">{{ userInfo.nickname || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>
			<!-- 姓名 -->
			<view class="profile-item" @click="editName">
				<view class="item-label">姓名</view>
				<view class="item-content">
					<text class="item-value">{{ userInfo.name || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 性别 -->
			<view class="profile-item" @click="editGender">
				<view class="item-label">性别</view>
				<view class="item-content">
					<text class="item-value">{{ userInfo.gender || '男' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 手机号 -->
			<view class="profile-item" @click="editPhone">
				<view class="item-label">手机号</view>
				<view class="item-content">
					<text class="item-value">{{ formatPhone(userInfo.phone) }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>

			<!-- 身份证号 -->
			<view class="profile-item" @click="editIdCardNumber">
				<view class="item-label">身份证号</view>
				<view class="item-content">
					<text class="item-value">{{ formatIdCardNumber(userInfo.idCardNumber) }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>
			<!-- 紧急联系人 -->
			<!-- <view class="profile-item" @click="editEmergencyContact">
				<view class="item-label">紧急联系人</view>
				<view class="item-content">
					<text class="item-value">{{ userInfo.emergencyContact || '未设置' }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view>
 -->
			<!-- 紧急联系人电话 -->
			<!-- <view class="profile-item" @click="editEmergencyPhone">
				<view class="item-label">紧急联系人电话</view>
				<view class="item-content">
					<text class="item-value">{{ formatPhone(userInfo.emergencyPhone) }}</text>
					<uni-icons type="right" size="18" color="#ccc"></uni-icons>
				</view>
			</view> -->
		</view>

		<!-- 编辑昵称弹窗 -->
		<uni-popup ref="nicknamePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置昵称</view>
					<view class="popup-close" @click="closePopup('nicknamePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="nickname"
						v-model="editForm.nickname"
						placeholder="请输入昵称"
						maxlength="20"
					/>
					<view class="input-counter">{{ editForm.nickname.length }}/20</view>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="saveNickname">确定</button>
				</view>
			</view>
		</uni-popup>
		<!-- 编辑姓名弹窗 -->
		<uni-popup ref="namePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置姓名</view>
					<view class="popup-close" @click="closePopup('namePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="text"
						v-model="editForm.name"
						placeholder="请输入姓名"
						maxlength="20"
					/>
					<view class="input-counter">{{ editForm.name.length }}/20</view>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="saveName">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 编辑性别弹窗 -->
		<uni-popup ref="genderPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置性别</view>
					<view class="popup-close" @click="closePopup('genderPopup')">取消</view>
				</view>
				<view class="popup-body">
					<radio-group @change="genderChange">
						<label class="radio-item" v-for="(item, index) in genderOptions" :key="index">
							<view class="radio-label">{{ item.label }}</view>
							<radio :value="item.value" :checked="editForm.gender == item.value" color="#c8a287" />
						</label>
					</radio-group>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="saveGender">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 编辑手机号弹窗 -->
		<uni-popup ref="phonePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置手机号</view>
					<view class="popup-close" @click="closePopup('phonePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="phone"
						v-model="editForm.phone"
						placeholder="请输入手机号"
						maxlength="11"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="savePhone">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 编辑身份证号弹窗 -->
		<uni-popup ref="idCardNumberPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置身份证号</view>
					<view class="popup-close" @click="closePopup('idCardNumberPopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="text"
						v-model="editForm.idCardNumber"
						placeholder="请输入身份证号"
						maxlength="18"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="saveIdCardNumber">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 编辑紧急联系人弹窗 -->
		<uni-popup ref="emergencyContactPopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置紧急联系人</view>
					<view class="popup-close" @click="closePopup('emergencyContactPopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="text"
						v-model="editForm.emergencyContact"
						placeholder="请输入紧急联系人姓名"
						maxlength="20"
					/>
					<view class="input-counter">{{ editForm.emergencyContact.length }}/20</view>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="saveEmergencyContact">确定</button>
				</view>
			</view>
		</uni-popup>

		<!-- 编辑紧急联系人电话弹窗 -->
		<uni-popup ref="emergencyPhonePopup" type="bottom">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">设置紧急联系人电话</view>
					<view class="popup-close" @click="closePopup('emergencyPhonePopup')">取消</view>
				</view>
				<view class="popup-body">
					<input
						class="input-field"
						type="number"
						v-model="editForm.emergencyPhone"
						placeholder="请输入紧急联系人电话"
						maxlength="11"
					/>
				</view>
				<view class="popup-footer">
					<button class="confirm-btn" @click="saveEmergencyPhone">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				name: '',
				nickname: '',
				avatar: '',
				account:'',
				gender: '男', // 0-未知，1-男，2-女
				phone: '',
				idCardNumber: '',
				emergencyContact: '',
				emergencyPhone: ''
			},
			editForm: {
				name: '',
				nickname: '',
				gender: '男',
				phone: '',
				idCardNumber: '',
				emergencyContact: '',
				emergencyPhone: ''
			},
			genderOptions: [
				{ label: '男', value: '男' },
				{ label: '女', value: '女' }
			]
		}
	},
	onLoad() {
		this.getUserInfo();
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		getUserInfo() {
			const userInfo = uni.getStorageSync('userInfo');
			if (userInfo) {
				this.userInfo = JSON.parse(userInfo);
			} else {
				uni.request({
					url: this.$backUrl + '/auth/c/getLoginUser',
					method: 'GET',
					header: {
						'token': uni.getStorageSync('token')
					},
					success: (res) => {
						this.userInfo = {...res.data.data};
						uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));
					}
				});
			}
		},
		formatPhone(phone) {
			if (!phone) return '未设置';
			// 格式化手机号为 136****5678 的形式
			if (phone.length === 11) {
				return phone.substring(0, 3) + '******' + phone.substring(9);
			}
			return phone;
		},
		// 格式化身份证号
		formatIdCardNumber(idCardNumber) {
			if (!idCardNumber) return '未设置';
			// 格式化身份证号为 123456********1234 的形式
			if (idCardNumber.length === 18) {
				return idCardNumber.substring(0, 6) + '********' + idCardNumber.substring(14);
			}
			return idCardNumber;
		},
		onChooseAvatar(e) {
			const avatarUrl = e.detail.avatarUrl;
			console.log('avatar', avatarUrl);
			this.imageToBase64(avatarUrl, (base64) => {
				this.userInfo.avatar = base64;
				this.saveUserInfo();

				uni.showToast({
					title: '头像更新成功',
					icon: 'success'
				});
			});
		},
		// 编辑头像
		editAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];

					// 将图片转换为base64
					this.imageToBase64(tempFilePath, (base64) => {
						// 更新头像为base64字符串
						this.userInfo.avatar = base64;
						this.saveUserInfo();

						uni.showToast({
							title: '头像更新成功',
							icon: 'success'
						});
					});
				}
			});
		},

		// 图片转base64
		imageToBase64(filePath, callback) {
			// 读取图片文件
			uni.getFileSystemManager().readFile({
				filePath: filePath,
				encoding: 'base64',
				success: (res) => {
					// 获取图片类型
					let imageType = 'image/jpeg'; // 默认类型
					if (filePath.endsWith('.png')) {
						imageType = 'image/png';
					} else if (filePath.endsWith('.gif')) {
						imageType = 'image/gif';
					}

					// 拼接完整的base64字符串
					const base64String = `data:${imageType};base64,${res.data}`;
					callback(base64String);
				},
				fail: (err) => {
					console.error('图片转base64失败', err);
					uni.showToast({
						title: '图片处理失败',
						icon: 'none'
					});
				}
			});
		},
		// 编辑昵称
		editNickname() {
			this.editForm.nickname = this.userInfo.nickname;
			this.$refs.nicknamePopup.open();
		},
		// 保存昵称
		saveNickname() {
			if (!this.editForm.nickname.trim()) {
				uni.showToast({
					title: '昵称不能为空',
					icon: 'none'
				});
				return;
			}

			this.userInfo.nickname = this.editForm.nickname;
			this.saveUserInfo();
			this.$refs.nicknamePopup.close();

			uni.showToast({
				title: '昵称更新成功',
				icon: 'success'
			});
		},
		// 编辑姓名
		editName() {
			this.editForm.name = this.userInfo.name;
			this.$refs.namePopup.open();
		},
		// 保存姓名
		saveName() {
			if (!this.editForm.name.trim()) {
				uni.showToast({
					title: '姓名不能为空',
					icon: 'none'
				});
				return;
			}

			this.userInfo.name = this.editForm.name;
			this.saveUserInfo();
			this.$refs.namePopup.close();

			uni.showToast({
				title: '姓名更新成功',
				icon: 'success'
			})
		},
		// 编辑性别
		editGender() {
			this.editForm.gender = this.userInfo.gender;
			this.$refs.genderPopup.open();
		},
		// 性别选择变更
		genderChange(e) {
			this.editForm.gender = parseInt(e.detail.value);
		},
		// 保存性别
		saveGender() {
			this.userInfo.gender = this.editForm.gender;
			this.saveUserInfo();
			this.$refs.genderPopup.close();

			uni.showToast({
				title: '性别更新成功',
				icon: 'success'
			});
		},
		// 编辑手机号
		editPhone() {
			this.editForm.phone = this.userInfo.phone;
			this.$refs.phonePopup.open();
		},
		// 保存手机号
		savePhone() {
			if (!this.editForm.phone || this.editForm.phone.length !== 11) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			this.userInfo.phone = this.editForm.phone;
			this.saveUserInfo();
			this.$refs.phonePopup.close();

			uni.showToast({
				title: '手机号更新成功',
				icon: 'success'
			});
		},
		// 编辑身份证号
		editIdCardNumber() {
			this.editForm.idCardNumber = this.userInfo.idCardNumber || '';
			this.$refs.idCardNumberPopup.open();
		},
		// 保存身份证号
		saveIdCardNumber() {
			// 验证身份证号格式
			if (this.editForm.idCardNumber && !this.validateIdCardNumber(this.editForm.idCardNumber)) {
				uni.showToast({
					title: '请输入正确的身份证号',
					icon: 'none'
				});
				return;
			}

			this.userInfo.idCardNumber = this.editForm.idCardNumber;
			this.saveUserInfo();
			this.$refs.idCardNumberPopup.close();

			uni.showToast({
				title: '身份证号更新成功',
				icon: 'success'
			});
		},
		// 验证身份证号
		validateIdCardNumber(idCardNumber) {
			if (!idCardNumber) return true; // 允许为空
			// 简单的身份证号验证：18位数字，最后一位可以是X
			const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
			return idCardRegex.test(idCardNumber);
		},
		// 关闭弹窗
		closePopup(ref) {
			this.$refs[ref].close();
		},
		// 编辑紧急联系人
		editEmergencyContact() {
			this.editForm.emergencyContact = this.userInfo.emergencyContact || '';
			this.$refs.emergencyContactPopup.open();
		},
		// 保存紧急联系人
		saveEmergencyContact() {
			if (!this.editForm.emergencyContact.trim()) {
				uni.showToast({
					title: '紧急联系人不能为空',
					icon: 'none'
				});
				return;
			}

			this.userInfo.emergencyContact = this.editForm.emergencyContact;
			this.saveUserInfo();
			this.$refs.emergencyContactPopup.close();

			uni.showToast({
				title: '紧急联系人更新成功',
				icon: 'success'
			});
		},
		// 编辑紧急联系人电话
		editEmergencyPhone() {
			this.editForm.emergencyPhone = this.userInfo.emergencyPhone || '';
			this.$refs.emergencyPhonePopup.open();
		},
		// 保存紧急联系人电话
		saveEmergencyPhone() {
			if (!this.editForm.emergencyPhone || this.editForm.emergencyPhone.length !== 11) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			this.userInfo.emergencyPhone = this.editForm.emergencyPhone;
			this.saveUserInfo();
			this.$refs.emergencyPhonePopup.close();

			uni.showToast({
				title: '紧急联系人电话更新成功',
				icon: 'success'
			});
		},
		// 保存用户信息
		saveUserInfo() {
			uni.request({
				url: this.$backUrl + '/client/c/user/edit',
				method: 'POST',
				header: {
					'token': uni.getStorageSync('token')
				},
				data: {
					id: this.userInfo.id,
					account: this.userInfo.account,
					avatar: this.userInfo.avatar,
					nickname: this.userInfo.nickname,
					name: this.userInfo.name || '未设置',
					gender: this.userInfo.gender,
					phone: this.userInfo.phone,
					idCardNumber: this.userInfo.idCardNumber,
					// emergencyContact: this.userInfo.emergencyContact,
					// emergencyPhone: this.userInfo.emergencyPhone
				},
				success: (res) => {
					console.log('用户信息更新成功', res);
					uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));
				},
				fail: (err) => {
					console.error('用户信息更新失败', err);
				}
			});

		}
	}
}
</script>

<style>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.nav-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	background-color: white;
	position: sticky;
	top: 0;
	z-index: 10;
}

.page-title {
	font-size: 18px;
	font-weight: bold;
}

.profile-list {
	margin-top: 10px;
	background-color: white;
}

.profile-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.profile-item:last-child {
	border-bottom: none;
}

.item-label {
	font-size: 16px;
	color: #333;
}

.item-content {
	display: flex;
	align-items: center;
}

.item-value {
	font-size: 16px;
	color: #666;
	margin-right: 10px;
}

.avatar-wrapper {
	padding: 0;
	width: 50px;
	height: 50px;
	margin-right: 10px;
}

.avatar-image {
	width: 50px;
	height: 50px;
	border-radius: 5px;
}

/* 弹窗样式 */
.popup-content {
	background-color: white;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
	overflow: hidden;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
}

.popup-title {
	font-size: 16px;
	font-weight: bold;
}

.popup-close {
	font-size: 14px;
	color: #666;
}

.popup-body {
	padding: 20px 15px;
}

.input-field {
	width: 100%;
	height: 40px;
	border-bottom: 1px solid #f0f0f0;
	font-size: 16px;
}

.input-counter {
	text-align: right;
	font-size: 12px;
	color: #999;
	margin-top: 5px;
}

.radio-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
	border-bottom: 1px solid #f0f0f0;
}

.radio-item:last-child {
	border-bottom: none;
}

.radio-label {
	font-size: 16px;
}

.popup-footer {
	padding: 15px;
}

.confirm-btn {
	width: 100%;
	height: 44px;
	line-height: 44px;
	background-color: #c8a287;
	color: white;
	border-radius: 5px;
	font-size: 16px;
}
</style>
